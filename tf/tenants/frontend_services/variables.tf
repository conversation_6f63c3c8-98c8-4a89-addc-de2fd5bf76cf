variable "fs_aws_region" {
  description = "The AWS region things are created in"
}

variable "fs_env" {
  description = "The environment"
}

variable "fs_aws_account_id" {
  description = "The AWS account id"
}

variable "fs_web_domain_certificate_arn" {
  description = "The arn for *.meupagador.com.br certificate"
}

variable "fs_tenant" {
  description = "The tenant"
}

variable "fs_aws_azs" {
  description = "The availability zones"
  type        = list(string)
}

variable "fs_web_domain" {}

variable "fs_prevent_destroy" {
  type = bool
  #default = true
  default = false
}

variable "fs_force_destroy" {
  type = bool
  #default = false
  default = false
}