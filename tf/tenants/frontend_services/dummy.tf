resource "aws_s3_object" "dummt_static_lambda_proxy_request" {
  bucket = aws_s3_bucket.static_lambda_proxy.id
  key    = local.lambda_origin_request_handler_file
  source = local.file_dummy_lambda  # Caminho para o arquivo local
  etag   = filemd5(local.file_dummy_lambda)  # Opcional: para garantir a detecção de alterações

  lifecycle {
    ignore_changes = [
      etag, tags, tags_all
    ]
  }
}

resource "aws_s3_object" "dummy_static_lambda_proxy_response" {
  bucket = aws_s3_bucket.static_lambda_proxy.id
  key    = local.lambda_origin_response_handler_file
  source = local.file_dummy_lambda  # Caminho para o arquivo local
  etag   = filemd5(local.file_dummy_lambda)  # Opcional: para garantir a detecção de alterações

  lifecycle {
    ignore_changes = [
      etag, tags, tags_all
    ]
  }
}

