module "global" { source = "../../modules/global" }

data "aws_ssm_parameter" "web_domain_certificate_arn" {
  name = module.global.web_domain_certificate_arn_parameter_name
}

locals {
  web_domain_certificate_arn                   = data.aws_ssm_parameter.web_domain_certificate_arn.value
  bucket_policy_path                           = "${path.module}/bucket_policy.json"
  web_app_bucket_name                          = "${var.fs_tenant}-webapp-${var.fs_env}"
  notification_templates_bucket_name           = "${var.fs_tenant}-notification-templates-${var.fs_env}"
}

resource "aws_cloudfront_origin_access_identity" "webapp_origin_access_identity" {
  comment = "Webapp Origin Access Identity"
}

resource "aws_s3_bucket" "webapp" {
  bucket = local.web_app_bucket_name
  acl    = "private"
  policy = templatefile(local.bucket_policy_path, {
    user    = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.iam_arn
    bucket  = "${local.web_app_bucket_name}"
    pattern = "current/*"
  })
  /*lifecycle {
    prevent_destroy = true
  }*/
  #force_destroy = var.fs_force_destroy
  versioning {
    enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "webapp" {
  bucket = aws_s3_bucket.webapp.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "webapp" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.webapp.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.webapp.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  wait_for_deployment = false
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.webapp.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = aws_lambda_function.static_lambda_proxy_origin_request.qualified_arn
      # lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:${aws_lambda_function.static_lambda_proxy_origin_response.version}"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = aws_lambda_function.static_lambda_proxy_origin_response.qualified_arn
      # lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:${aws_lambda_function.static_lambda_proxy_origin_response.version}"
    }
  }
  aliases = ["use-${var.fs_tenant}.${var.fs_web_domain}"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  custom_error_response {
    error_code            = 403
    error_caching_min_ttl = 300
    response_code         = 200
    response_page_path    = "/index.html"
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
      # cloudfront_default_certificate = true
    cloudfront_default_certificate = false
    acm_certificate_arn      = local.web_domain_certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
}

resource "aws_cloudfront_origin_access_identity" "site_origin_access_identity" {
  comment = "Site Origin Access Identity"
}

/*
 * Lambda@Edge Static Proxies - Bucket
 */

resource "aws_s3_bucket" "static_lambda_proxy" {
  bucket = "${var.fs_tenant}-static-lambda-proxy-${var.fs_env}"
  acl    = "private"
  lifecycle {
    #prevent_destroy = var.fs_prevent_destroy
  }
  force_destroy = var.fs_force_destroy
  versioning {
    enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "static_lambda_proxy" {
  bucket = aws_s3_bucket.static_lambda_proxy.id

  block_public_acls   = true
  block_public_policy = true
}


/*
 * Lambda@Edge Static Proxies - Origin Request
 */

data "aws_iam_policy_document" "static_lambda_execution_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com", "edgelambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "static_lambda_proxy_origin_request" {
  name               = "static_lambda_proxy_origin_request"
  assume_role_policy = data.aws_iam_policy_document.static_lambda_execution_role.json
}

resource "aws_cloudwatch_log_group" "static_lambda_proxy_origin_request" {
  name              = "/aws/lambda/static-lambda-proxy-origin-request"
  retention_in_days = 14
}

resource "aws_iam_policy" "static_lambda_proxy_origin_request_logging" {
  name        = "static-lambda-proxy-origin-request-logging"
  path        = "/"
  description = "IAM policy for logging from a static lambda proxy for origin request"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${var.fs_aws_region}:*:log-group:/aws/lambda/static-lambda-proxy-origin-request:*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "static_lambda_proxy_origin_request_logging" {
  role       = aws_iam_role.static_lambda_proxy_origin_request.name
  policy_arn = aws_iam_policy.static_lambda_proxy_origin_request_logging.arn
}

// TODO: there is no assurance lambda is on correct region
resource "aws_lambda_function" "static_lambda_proxy_origin_request" {
  function_name = "static-lambda-proxy-origin-request"
  role          = aws_iam_role.static_lambda_proxy_origin_request.arn
  handler       = "index.handler"
  runtime       = "nodejs16.x"
  s3_bucket     = aws_s3_bucket.static_lambda_proxy.id
  s3_key        = "current/origin-request-handler.zip"
  depends_on = [aws_iam_role_policy_attachment.static_lambda_proxy_origin_request_logging,
    aws_cloudwatch_log_group.static_lambda_proxy_origin_request]

  lifecycle { ignore_changes = [publish] }
}

/*
 * Lambda@Edge Static Proxies - Origin Response
 */

resource "aws_iam_role" "static_lambda_proxy_origin_response" {
  name               = "static_lambda_proxy_origin_response"
  assume_role_policy = data.aws_iam_policy_document.static_lambda_execution_role.json
}

resource "aws_cloudwatch_log_group" "static_lambda_proxy_origin_response" {
  name              = "/aws/lambda/static-lambda-proxy-origin-response"
  retention_in_days = 14
}

resource "aws_iam_policy" "static_lambda_proxy_origin_response_logging" {
  name        = "static-lambda-proxy-origin-response-logging"
  path        = "/"
  description = "IAM policy for logging from a static lambda proxy for origin response"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${var.fs_aws_region}:*:log-group:/aws/lambda/static-lambda-proxy-origin-response:*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "static_lambda_proxy_origin_response_logging" {
  role       = aws_iam_role.static_lambda_proxy_origin_response.name
  policy_arn = aws_iam_policy.static_lambda_proxy_origin_response_logging.arn
}

// TODO: there is no assurance lambda is on correct region
resource "aws_lambda_function" "static_lambda_proxy_origin_response" {
  function_name = "static-lambda-proxy-origin-response"
  role          = aws_iam_role.static_lambda_proxy_origin_response.arn
  handler       = "index.handler"
  runtime       = "nodejs16.x"
  s3_bucket     = aws_s3_bucket.static_lambda_proxy.id
  s3_key        = "current/origin-response-handler.zip"
  depends_on = [aws_iam_role_policy_attachment.static_lambda_proxy_origin_response_logging,
    aws_cloudwatch_log_group.static_lambda_proxy_origin_response]
  publish = true

  lifecycle { ignore_changes = [publish] }
}

resource "aws_cloudfront_origin_access_identity" "notification_templates_origin_access_identity" {
  comment = "Notification Templates Identity"
}

resource "aws_s3_bucket" "notification_templates" {
  bucket = local.notification_templates_bucket_name
  acl    = "private"
  policy = templatefile(local.bucket_policy_path, {
    user    = aws_cloudfront_origin_access_identity.notification_templates_origin_access_identity.iam_arn
    bucket  = local.notification_templates_bucket_name
    pattern = "current/static/*"
  })
  lifecycle {
    #prevent_destroy = var.fs_prevent_destroy
  }
  #force_destroy = var.fs_force_destroy
  versioning {
    enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "notification_templates" {
  bucket = aws_s3_bucket.notification_templates.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "notification_templates" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.notification_templates.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.notification_templates.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.notification_templates_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  wait_for_deployment = false
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]

    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.notification_templates.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:${aws_lambda_function.static_lambda_proxy_origin_request.version}"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:${aws_lambda_function.static_lambda_proxy_origin_request.version}"
    }
  }
  aliases = ["${var.fs_tenant}-notification-templates-cdn.${var.fs_web_domain}"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
      # cloudfront_default_certificate = true
    acm_certificate_arn            = local.web_domain_certificate_arn
    cloudfront_default_certificate = false
    ssl_support_method             =  "sni-only"
    minimum_protocol_version       = "TLSv1.1_2016"
  }

  lifecycle {
    ignore_changes = [
      default_cache_behavior,
    ]
  }
}