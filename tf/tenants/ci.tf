locals {
  ci_policy_name   = "GitLabPipelinePolicy"
  role_suffixes    = ["-execution-role", "ExecutionRole", "-task-role"]
  s3_buckets       = [
    "${var.tenant}-static-lambda-proxy-${var.env}",
    "${var.tenant}-webapp-${var.env}",
    "${var.tenant}-contas-webapp-${var.env}"
  ]
}

resource "aws_ssm_parameter" "runner_vpc_id" {
  name  = module.global.runner_vpc_id_parameter_name
  type  = "String"
  value = module.backend_services.vpc_id
}

resource "aws_ssm_parameter" "pipeline_policy_arns" {
  name  = module.global.runner_pipeline_policy_arn_parameter_name
  type  = "String"
  value = aws_iam_policy.gitlab_ci_pipeline_policy.arn

  depends_on = [aws_iam_policy.gitlab_ci_pipeline_policy]
}

resource "aws_ssm_parameter" "runner_private_subnet_ids" {
  name = module.global.runner_private_subnet_ids_parameter_name
  type = "String"
  value = jsonencode(module.backend_services.private_subnets)
}

data "aws_iam_policy_document" "gitlab_ci_pipeline_policy" {
  statement {
    actions   = ["iam:PassRole"]
    resources = [for suffix in local.role_suffixes :"arn:aws:iam::${var.aws_account_id}:role/*${suffix}"]
  }

  statement {
    actions = [
      "s3:ListBucketVersions",
      "s3:ReplicateTags",
      "s3:ListBucket",
      "s3:PutObject",
      "s3:GetObject",
      "s3:PutBucketTagging",
      "s3:GetObjectTagging",
      "s3:PutObjectTagging",
      "s3:DeleteObject",
      "secretsmanager:GetSecretValue",
    ]
    resources = concat(
      [for bucket in local.s3_buckets :"arn:aws:s3:::${bucket}"],
      [for bucket in local.s3_buckets :"arn:aws:s3:::${bucket}/*"]
    )
  }

  statement {
    actions = [
      "ecr:BatchCheckLayerAvailability",
      "ecr:CompleteLayerUpload",
      "ecr:GetAuthorizationToken",
      "ecr:InitiateLayerUpload",
      "ecr:PutImage",
      "ecr:UploadLayerPart",
      "ecs:DescribeTaskDefinition",
      "ecs:DescribeServices",
      "ecs:RegisterTaskDefinition",
      "ecs:UpdateService",
      "ecs:ListTasks",
      "cloudfront:ListDistributions",
      "lambda:ListFunctions",
      "s3:ListAllMyBuckets",
    ]
    resources = ["*"]
  }

  statement {
    actions = [
      "lambda:EnableReplication",
      "lambda:GetFunctionConfiguration",
      "lambda:GetFunction",
      "lambda:UpdateFunctionCode",
    ]
    resources = [
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:static-lambda-proxy-origin-request",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:static-lambda-proxy-origin-response",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:static-lambda-proxy-origin-request:*",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:static-lambda-proxy-origin-response:*",
    ]
  }

  statement {
    actions = [
      "cloudfront:GetDistribution",
      "cloudfront:GetDistributionConfig",
      "cloudfront:UpdateDistribution",
    ]
    resources = [
      for dist in module.frontend_services.fs_aws_cloudfront_distributions :"arn:aws:cloudfront::${var.aws_account_id}:distribution/${dist}"
    ]
  }
}

resource "aws_iam_policy" "gitlab_ci_pipeline_policy" {
  name   = local.ci_policy_name
  path   = "/"
  policy = data.aws_iam_policy_document.gitlab_ci_pipeline_policy.json
}