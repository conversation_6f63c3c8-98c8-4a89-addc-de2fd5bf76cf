terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      configuration_aliases = [
        aws.principal,
        aws.replica,
      ]
    }
  }
}

resource "aws_kms_key" "connect_utility_key" {
  provider                = aws.principal
  description             = "KMS key for bill payment encryption"
  deletion_window_in_days = 10
  multi_region            = true
}

resource "aws_kms_alias" "connect_utility_key" {
  provider      = aws.principal
  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_key.connect_utility_key.key_id
}

resource "aws_kms_replica_key" "connect_utility_replica_key" {
  provider                = aws.replica
  description             = "KMS key for bill payment encryption replica"
  deletion_window_in_days = 10
  primary_key_arn         = aws_kms_key.connect_utility_key.arn
}

resource "aws_kms_alias" "connect_utility_replica_alias" {
  provider      = aws.replica
  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_replica_key.connect_utility_replica_key.key_id
}