provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [var.aws_account_id]

  default_tags {
    tags = {
      Environment     = var.env
      ManagedBy       = "Terraform"
    }
  }
}

terraform {
  backend "s3" {
    key           = "tenant/terraform.tfstate"
    encrypt        = true
  }
}

data "aws_caller_identity" "current" {}

data "aws_dynamodb_table" "tf-backend-table" {
  name = var.dynamodb_table_name
}

data "aws_s3_bucket" "terraform_backend_state" {
  bucket = var.backend_bucket_name
}

data "aws_ssm_parameter" "web_domain_certificate_arn" {
  name = module.global.web_domain_certificate_arn_parameter_name
}

module "global" { source = "../modules/global" }