module "frontend_services" {
  source                         = "./frontend_services"
  fs_aws_account_id              = local.aws_account_id
  fs_aws_region                  = var.aws_region
  fs_env                         = var.env
  fs_tenant                      = var.tenant
  fs_aws_azs                     = var.aws_azs
  fs_web_domain_certificate_arn  = data.aws_ssm_parameter.web_domain_certificate_arn.value
  fs_web_domain                  = var.web_domain
}

resource "aws_ssm_parameter" "webapp_cloudfront_distribution_id" {
  name = module.global.webapp_cf_distribution_id_parameter_name
  type = "String"
  value = module.frontend_services.fs_aws_cloudfront_distribution_webapp_id
}

resource "aws_ssm_parameter" "notification_templates_cloudfront_distribution_id" {
  name = module.global.notification_templates_cf_distribution_id_parameter_name
  type = "String"
  value = module.frontend_services.fs_aws_cloudfront_distribution_notification_templates_id
}