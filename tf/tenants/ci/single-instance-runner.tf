module "single-runner-ci" {
  count = var.runner_strategy == "instance" ? 1 : 0
  source      = "cattle-ops/gitlab-runner/aws"
  environment = var.gitlab_env

  iam_object_prefix     = var.gitlab_env
  security_group_prefix = var.gitlab_env

  runner_enable_asg_recreation = true

  vpc_id    = local.runner_vpc_id
  subnet_id = local.private_subnet_id
  version   = "7.14.0"

  runner_worker_docker_volumes_tmpfs = [
    {
      volume  = "/var/opt/cache",
      options = "rw,noexec"
    }
  ]

  runner_gitlab = {
    url                                           = "https://gitlab.com"
    preregistered_runner_token_ssm_parameter_name = aws_ssm_parameter.gitlab_group_token.name
    runner_version                                = "17.4.0"
  }

  runner_worker_cache = {
    type = "s3"
    s3 = {
      bucket_name = "${var.aws_account_id}-${var.gitlab_env}-single-runner-cache"
    }
  }

  runner_instance = {
    name                 = "docker-ci"
    private_address_only = true
    ssm_access           = true
    type                 = element(local.instance_types, 0)
    monitoring           = false
    max_lifetime_seconds = 3600 * 24

    root_device_config = {
      volume_size       = 100
      volume_type       = "gp3"
      volume_throughput = 125
      iops              = 3000
      encrypted         = false
    }
  }

  runner_worker = {
    type       = "docker"
    ssm_access = true
    environment_variables = [var.tenant, var.gitlab_env]

    max_jobs = 2
    request_concurrency = 2
  }

  runner_schedule_enable = true

  runner_schedule_config = {
    scale_out_time_zone        = "America/Sao_Paulo"
    scale_out_recurrence       = "0 8 * * 1-5"
    scale_out_min_size         = 0
    scale_out_desired_capacity = 1
    scale_out_max_size         = 5

    scale_in_time_zone        = "America/Sao_Paulo"
    scale_in_recurrence       = "0 20 * * *"
    scale_in_min_size         = 0
    scale_in_desired_capacity = 0
    scale_in_max_size         = 1
  }

  runner_role = {
    policy_arns = local.pipeline_policy_arns
  }

  runner_worker_docker_machine_role = {
    policy_arns = local.pipeline_policy_arns
  }

  runner_worker_docker_options = {
    privileged  = "true"
    pull_policy = "if-not-present"
    pull_policies = ["if-not-present"]
    volumes = ["/cache", "/var/run/docker.sock:/var/run/docker.sock", "/tmp/builds:/tmp/builds"]
  }

  runner_install = {
    post_install_script = ""
  }

  runner_cloudwatch = {
    enabled        = false
    retention_days = 3
  }

  tags = {
    Component = module.global.component_ci
    Environment = var.gitlab_env
    ManagedBy = "Terraform"
  }
}