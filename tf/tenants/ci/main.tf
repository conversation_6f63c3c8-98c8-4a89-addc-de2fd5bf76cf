module "global" { source = "../../modules/global" }

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.25"
    }
  }
}

terraform {
  backend "s3" {
    key           = "tenant/terraform-ci.tfstate"
    encrypt        = true
  }
}

provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [var.aws_account_id]

  default_tags {
    tags = {
      Environment     = var.env
      ManagedBy       = "Terraform"
      Component       = module.global.component_ci
    }
  }
}

data "aws_caller_identity" "current" {}

data "aws_ssm_parameter" "runner_pipeline_policy_arn" { name = module.global.runner_pipeline_policy_arn_parameter_name }

data "aws_ssm_parameter" "private_subnet_ids" { name = module.global.runner_private_subnet_ids_parameter_name }

data "aws_ssm_parameter" "runner_vpc_id" { name = module.global.runner_vpc_id_parameter_name }