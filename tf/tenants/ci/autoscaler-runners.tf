locals {
  as_prefix = "autoscale-${var.gitlab_env}"
}

module "autoscale-group-runner-ci" {
  count = var.runner_strategy == "autoscaler" ? 1 : 0

  source      = "cattle-ops/gitlab-runner/aws"
  environment = var.gitlab_env

  version = "7.14.0"

  iam_object_prefix     = local.as_prefix
  security_group_prefix = local.as_prefix

  vpc_id    = local.runner_vpc_id
  subnet_id = local.private_subnet_id

  runner_instance = {
    name = "${var.tenant}-autoscale-runner-ci"
    ssm_access = true
    collect_autoscaling_metrics = ["GroupDesiredCapacity", "GroupInServiceCapacity"]
  }

  runner_networking = {
    allow_incoming_ping = true
  }

  runner_gitlab = {
    url                                           = "https://gitlab.com"
    preregistered_runner_token_ssm_parameter_name = aws_ssm_parameter.gitlab_group_token.name
    runner_version                                = "17.4.0"
  }

  runner_manager = {
    maximum_concurrent_jobs = 5
  }

  runner_worker = {
    type = "docker-autoscaler"
    environment_variables = [var.tenant, var.gitlab_env]
    max_jobs            = 5
    request_concurrency = 5
  }

  runner_worker_docker_autoscaler = {
    fleeting_plugin_version = "1.0.0"
    connector_config_user = "ubuntu"
  }

  runner_worker_docker_autoscaler_instance = {
    volume_type = "gp3"
    monitoring = true
  }

  runner_worker_docker_autoscaler_asg = {
    subnet_ids                               = local.private_subnet_ids
    types                                    = local.instance_types
    enable_mixed_instances_policy            = true
    on_demand_base_capacity                  = 1
    on_demand_percentage_above_base_capacity = 0
    max_growth_rate                          = 10
    spot_allocation_strategy                 = "capacity-optimized"
    spot_instance_pools                      = 0
  }

  runner_worker_docker_autoscaler_autoscaling_options = [
    {
      periods            = ["* * * * *"]
      timezone           = local.timezone
      idle_count         = 0
      idle_time    = "10m"
      scale_factor = 2
      scale_factor_limit = 5
    },
    {
      periods            = ["* 7-19 * * mon-fri"]
      timezone           = local.timezone
      idle_count         = 0
      idle_time    = "30m"
      scale_factor = 2
      scale_factor_limit = 5
    }
  ]

  runner_worker_cache = {
    type = "s3"
    s3 = {
      bucket_name = "${var.aws_account_id}-${var.gitlab_env}-autoscaler-runner-cache"
    }
  }

  runner_worker_docker_options = {
    volumes = ["/cache", "/var/run/docker.sock:/var/run/docker.sock"]
  }

  tags = {
    Component   = module.global.component_ci
    Environment = var.gitlab_env
    ManagedBy   = "Terraform"
  }
}