locals {
  pipeline_policy_arns = [data.aws_ssm_parameter.runner_pipeline_policy_arn.value]
  private_subnet_id = element(jsondecode(data.aws_ssm_parameter.private_subnet_ids.value), 0)
  private_subnet_ids = jsondecode(data.aws_ssm_parameter.private_subnet_ids.value)
  runner_vpc_id = data.aws_ssm_parameter.runner_vpc_id.value

  timezone       = "America/Sao_Paulo"

  instance_types = [
    "c7i.2xlarge",
    "c6i.2xlarge",
    "c6a.2xlarge",
    "c5a.2xlarge",
    "c5ad.2xlarge",
    "m6a.2xlarge",
    "c5.2xlarge",
    "m5a.2xlarge",
    "t3.2xlarge",
    "t3a.2xlarge",
  ]

  #  amd_instances   = ["c6a.2xlarge", "c5a.2xlarge", "t3a.2xlarge", "m6a.2xlarge", "c5ad.2xlarge", "m5a.2xlarge"]
  #  intel_instances = ["c7i.2xlarge", "c6i.2xlarge", "t3.2xlarge", "c5.2xlarge"]
  #  distinct(concat(local.intel_instances, local.amd_instances))
}