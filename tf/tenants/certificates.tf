resource "aws_acm_certificate" "web_domain" {
  domain_name               = var.web_domain
  subject_alternative_names = ["*.${var.web_domain}"]
  validation_method         = "DNS"
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    ignore_changes = [
      private_key,
      certificate_body,
    ]
  }
}

# generate a dummy private key
resource "tls_private_key" "self_signed" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

# self-signed certificate
resource "tls_self_signed_cert" "self_signed" {
  allowed_uses = ["key_encipherment", "digital_signature", "server_auth"]

  private_key_pem = tls_private_key.self_signed.private_key_pem

  subject {
    common_name  = var.web_domain
    organization = "Friday"
  }

  validity_period_hours = 2562047
  early_renewal_hours   = 720

  dns_names = [var.web_domain, "www.${var.web_domain}"]

  is_ca_certificate = false
}

# self-signed acm certificate
resource "aws_acm_certificate" "dummy" {
  private_key = tls_private_key.self_signed.private_key_pem
  certificate_body = tls_self_signed_cert.self_signed.cert_pem
}

# reference of certificate arn to be used in other resources
resource "aws_ssm_parameter" "web_domain_cert_arn" {
  name  = module.global.web_domain_certificate_arn_parameter_name
  type  = "String"
  value = local.web_domain_certificate_arn

  lifecycle {
    prevent_destroy = true
  }
}