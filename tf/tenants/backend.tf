provider "aws" {
  alias  = "sp"
  region = "sa-east-1"

  default_tags {
    tags = {
      Environment = "staging"
      Project     = "connect-utility"
    }
  }
}

# module "connect_utility" {
#    source = "./connect_utility"
#   providers = {
#     aws.principal = aws
#     aws.replica = aws.sp
#   }
# }

module "backend_services" {
  source                     = "./backend_services"
  bs_aws_account_id          = local.aws_account_id
  bs_aws_region              = var.aws_region
  bs_env                     = var.env
  bs_tenant                  = var.tenant
  bs_web_domain              = var.web_domain
  bs_email_domain            = var.web_domain
  bs_config_files_bucket_arn = aws_s3_bucket.fluent_bit_configs.arn

  bs_user_pool_arn_enabled         = var.user_pool_arn_enabled
  bs_kms_key_arns = [/*module.connect_utility.connect_utility_key_arn*/]
  bs_aws_azs                       = var.aws_azs
  bs_web_domain_certificate_arn    = data.aws_ssm_parameter.web_domain_certificate_arn.value
  bs_jazz_enabled                  = var.jazz_enabled
  bs_chatbot_adicional_secrets_map = var.chatbot_adicional_secrets_map
  bs_chatbot_adicional_tenants     = var.chatbot_adicional_tenants
  bs_chatbot_enabled               = var.chatbot_enabled
}

resource "aws_s3_bucket" "fluent_bit_configs" {
  bucket = local.config_files_bucket_name
}

resource "aws_s3_object" "fluent_bit_general_conf" {
  bucket = aws_s3_bucket.fluent_bit_configs.bucket
  key    = "config/general.conf"
  source = "../files/fluent-bit/general.conf"
  acl    = "private"
}

resource "aws_s3_object" "fluent_bit_parsers_conf" {
  bucket = aws_s3_bucket.fluent_bit_configs.bucket
  key    = "config/parsers.conf"
  source = "../files/fluent-bit/parsers.conf"
  acl    = "private"
}