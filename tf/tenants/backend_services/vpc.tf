module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.13.0"
  name    = "${var.bs_tenant}-vpc"
  cidr    = "10.0.0.0/16"

  azs                     = var.bs_aws_azs
  public_subnets          = local.public_subnets
  private_subnets         = local.private_subnets

  enable_nat_gateway      = var.bs_nat_gateway_enabled
  single_nat_gateway      = true
  enable_vpn_gateway      = false
  enable_dns_hostnames    = true
  enable_dns_support      = true
  map_public_ip_on_launch = true

  database_subnet_group_name         = "${var.bs_tenant}-db-subnet-group"
  create_database_subnet_group       = true
  create_database_subnet_route_table = false
  database_subnets                   = local.database_subnets
}
