output "public_subnets_id" {
  value = module.vpc.public_subnets
}

output "private_subnets_id" {
  value = module.vpc.private_subnets
}

output "security_group" {
  value = module.vpc.default_security_group_id
}

output "nat_gateway_ips" {
  value = module.vpc.nat_public_ips
}

output "user_table_arn" {
  value = try(module.bill-payment-service[0].user_table_arn, "")
}

output "bill_payment_api_alb_hostname" {
  value = try(module.bill-payment-service[0].bill_payment_api_alb_hostname, "")
}

output "bill_payment_api_alb_alb_zone_id" {
  value = try(module.bill-payment-service[0].bill_payment_api_alb_alb_zone_id, "")
}

output "wallet_event_sns_topic_arn" {
  value = try(module.bill-payment-service[0].wallet_events_sns_topic_arn, "")
}

output "bill_payment_elasticache_url" {
  value = try(module.bill-payment-service[0].bill_payment_elasticache_url, "")
}

output "incoming_emails_sns_arn" {
  value = try(module.bill-payment-service[0].incoming_emails_sns_arn, "")
}

output "incoming_emails_s3_arn" {
  value = try(module.bill-payment-service[0].incoming_emails_s3_arn, "")
}


output "private_subnets" {
  value = module.vpc.private_subnets
}

output "public_subnets" {
  value = module.vpc.public_subnets
}

output "vpc_id" {
  value = module.vpc.vpc_id
}