variable "bs_tenant" {}

variable "bs_env" {}

variable "bs_email_domain" {}

variable "bs_web_domain" {}

variable "bs_config_files_bucket_arn" {}

variable "bs_datadog_key" {
  nullable = true
  default  = null
}

variable "bs_nat_gateway_enabled" {
  default = true
}

variable "bs_aws_account_id" {}

variable "bs_aws_region" {}

variable "bs_aws_azs" {}

variable "bs_web_domain_certificate_arn" {
  type = string
}

variable "bs_kms_key_arns" {
  type = list(string)
}

variable "bs_user_pool_arn_enabled" {
  type = bool
}

variable "bs_jazz_enabled" {
  type = bool
  default = false
}

variable "bs_chatbot_adicional_secrets_map" {
  type = map(string)
}

variable "bs_chatbot_adicional_tenants" {
  type = list(string)
}

variable "bs_chatbot_enabled" {
  type = bool
}