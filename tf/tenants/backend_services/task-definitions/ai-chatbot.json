[
  {
    "name": "AIChatbot",
    "image": "${app_image}",
    "cpu": 1014,
    "memory": 3789,
    "essential": true,
    "environment": [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "${app_environments}"
      },
      {
        "name": "JAVA_OPTS",
        "value": "-Xmx2048m"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      }
      ],
    "secrets": [
      %{ for name, value in secrets_map }
        {
          "name": "${name}",
          "valueFrom": "${value}"
        }
        %{if name != keys(secrets_map)[length(keys(secrets_map))-1]}
          ,
        %{endif}
      %{ endfor ~}
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "ai-chatbot"
    },
    "portMappings": [
      {
        "containerPort": 8443,
        "hostPort": 8443,
        "protocol": "tcp"
      }
    ],
    "systemControls": [],
    "volumesFrom": [],
    "mountPoints": [],
    "logConfiguration": {
      "logDriver": "awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/ai-chatbot-task",
        "auto_create_group": "false",
        "log_stream_name": "ecs/ai-chatbot-task/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "public.ecr.aws/datadog/agent:7.57.1",
    "cpu": 10,
    "memory": 256,
    "essential": false,
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secrets_map["DD_API_KEY"]}"
      }
    ],
    "environment": [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "protocol": "tcp",
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/ai-chatbot-task-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "systemControls": [],
    "volumesFrom": [],
    "mountPoints": []
  },
  {
    "image": "public.ecr.aws/aws-observability/aws-for-fluent-bit:init-latest",
    "name": "log_router",
    "essential": true,
    "cpu": 0,
    "portMappings": [],
    "user": "0",
    "volumesFrom": [],
    "firelensConfiguration": {
      "type": "fluentbit"
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secrets_map["DD_API_KEY"]}"
      }
    ],
    "environment": [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "AIChatbot"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/ai-chatbot-task"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      },
      {
        "name": "aws_fluent_bit_init_s3_1",
        "value": "arn:aws:s3:::${account_id}-${tenant}-config-files/config/general.conf"
      },
      {
        "name": "aws_fluent_bit_init_s3_2",
        "value": "arn:aws:s3:::${account_id}-${tenant}-config-files/config/parsers.conf"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50,
    "mountPoints": [],
    "systemControls": []
  }
]