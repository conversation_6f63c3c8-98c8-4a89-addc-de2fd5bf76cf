[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": ${cpu},
    "memory": ${memory},
    "essential": true,
    "mountPoints": [],
    "systemControls": [],
    "volumesFrom": [],
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "${app_environments}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },{
        "name": "DD_SERVICE",
        "value": "${service_name}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port},
        "protocol": "tcp"
      }
    ],
    "logConfiguration": {
      "logDriver":"awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${task_name}",
        "auto_create_group": "false",
        "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "public.ecr.aws/datadog/agent:7.56.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "mountPoints": [],
    "systemControls": [],
    "volumesFrom": [],
  "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125,
        "protocol": "tcp"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "image": "public.ecr.aws/aws-observability/aws-for-fluent-bit:init-latest",
    "name": "log_router",
    "essential": true,
    "cpu":0,
    "portMappings"          : [],
    "user"                  : "0",
    "volumesFrom"           : [],
    "mountPoints"           : [],
    "systemControls"        : [],
    "firelensConfiguration": {
      "type": "fluentbit"
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment" : [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "${container_name}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/${task_name}"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      },
      {
        "name": "aws_fluent_bit_init_s3_1",
        "value": "arn:aws:s3:::${account_id}-${tenant}-config-files/config/general.conf"
      },
      {
        "name": "aws_fluent_bit_init_s3_2",
        "value": "arn:aws:s3:::${account_id}-${tenant}-config-files/config/parsers.conf"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50
  }
]