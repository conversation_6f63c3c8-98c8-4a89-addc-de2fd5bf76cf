[
  {
    "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "image": "${app_image}",
    "cpu": ${cpu},
    "memory": ${memory},
    "essential": true,
    "stopTimeout": 120,
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "${app_environments}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "secrets": [
      %{ for env, value in secrets_map }
      {
        "name": "${env}",
        "valueFrom": "${value}"
      },
      %{ endfor ~}
      {
        "name": "ARBI_CALLBACK_SECRET",
        "valueFrom": "${secret_arns[1]}:ARBI_CALLBACK_SECRET::"
      },
      {
        "name": "ARBI_CALLBACK_IDENTITY",
        "valueFrom": "${secret_arns[1]}:ARBI_CALLBACK_IDENTITY::"
      },
      {
        "name": "MICRONAUT_HTTP_SERVICES_ARBI_SSL_KEY_STORE_PASSWORD",
        "valueFrom": "${secret_arns[1]}:ARBI_MTLS_PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_ARBI_CLIENT_SECRET",
        "valueFrom": "${secret_arns[2]}:CLIENT_SECRET::"
      },
      {
        "name": "INTEGRATIONS_ARBI_USER_TOKEN",
        "valueFrom": "${secret_arns[2]}:USER_TOKEN::"
      },
      {
        "name": "INTEGRATIONS_ARBI_CLIENT_ID",
        "valueFrom": "${secret_arns[2]}:CLIENT_ID::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_CLIENT",
        "valueFrom": "${secret_arns[3]}:CLIENT::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_API_KEY",
        "valueFrom": "${secret_arns[3]}:API_KEY::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_USERNAME",
        "valueFrom": "${secret_arns[3]}:USERNAME::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_PASSWORD",
        "valueFrom": "${secret_arns[3]}:PASSWORD::"
      },
      {
        "name": "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH",
        "valueFrom": "${secret_arns[4]}"
      },
      {
        "name": "INTEGRATIONS_CLEARSALE_HOST",
        "valueFrom": "${secret_arns[5]}:HOST::"
      },
      {
        "name": "INTEGRATIONS_CLEARSALE_USERNAME",
        "valueFrom": "${secret_arns[5]}:USERNAME::"
      },
      {
        "name": "INTEGRATIONS_CLEARSALE_PASSWORD",
        "valueFrom": "${secret_arns[5]}:PASSWORD::"
      },
      {
        "name": "TENANT_ENCRYPTION_MASTERKEY",
        "valueFrom": "${secret_arns[6]}:MASTERKEY::"
      },
      {
        "name": "INTEGRATIONS_SOFTWARE_EXPRESS_CREDENTIALS_MERCHANT_ID",
        "valueFrom": "${secret_arns[8]}:MERCHANT_ID::"
      },
      {
        "name": "INTEGRATIONS_SOFTWARE_EXPRESS_CREDENTIALS_MERCHANT_KEY",
        "valueFrom": "${secret_arns[8]}:MERCHANT_KEY::"
      }
    ],
    "portMappings": [
      {
        "protocol": "tcp",
        "containerPort": 8443,
        "hostPort": 8443
      }
    ],
    "volumesFrom": [],
    "mountPoints": [],
    "systemControls": [],
    "logConfiguration": {
      "logDriver":"awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${task_name}",
        "auto_create_group": "false",
          "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "public.ecr.aws/datadog/agent:7.57.1",
    "cpu": 10,
    "memory": 256,
    "essential": false,
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "portMappings": [
      {
        "protocol": "tcp",
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "volumesFrom": [],
    "mountPoints": [],
    "systemControls": []
  },
  {
    "essential": true,
    "image": "public.ecr.aws/aws-observability/aws-for-fluent-bit:init-latest",
    "name": "log_router",
    "firelensConfiguration": {
      "type": "fluentbit"
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment" : [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "${container_name}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/${task_name}"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      },
      {
        "name": "aws_fluent_bit_init_s3_1",
        "value": "arn:aws:s3:::${account_id}-${tenant}-config-files/config/general.conf"
      },
      {
        "name": "aws_fluent_bit_init_s3_2",
        "value": "arn:aws:s3:::${account_id}-${tenant}-config-files/config/parsers.conf"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50,
    "volumesFrom": [],
    "mountPoints": [],
    "systemControls": [],
    "portMappings": [],
    "user": "0",
    "cpu": 0
  }
]