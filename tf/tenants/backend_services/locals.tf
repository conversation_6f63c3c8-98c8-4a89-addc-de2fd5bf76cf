locals {
  main_vpc_id                         = module.vpc.vpc_id
  public_subnets                       = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnets                      = ["10.0.101.0/24", "10.0.102.0/24"]
  database_subnets                     = ["10.0.151.0/24", "10.0.152.0/24"]
  web_domain_certificate_arn           = var.bs_web_domain_certificate_arn

  env_prefix = {
    production = "prod"
    prod = "prod"
    staging  = "stg"
    stg  = "stg"
  }[var.bs_env]
}