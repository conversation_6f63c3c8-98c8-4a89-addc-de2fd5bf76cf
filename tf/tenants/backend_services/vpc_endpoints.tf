locals {
  enable_cw_logs_vpc_endpoint = true
  enable_dynamodb_vpc_endpoint = true
  enable_ecr_vpc_endpoint = true
  enable_s3_vpc_endpoint = true
  enable_sqs_vpc_endpoint = true
}

# VPC Endpoint individually defined

resource "aws_vpc_endpoint" "cloudwatch_logs" {
  count = local.enable_cw_logs_vpc_endpoint ? 1 : 0

  vpc_id              = local.main_vpc_id
  service_name        = "com.amazonaws.${var.bs_aws_region}.logs"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true

  subnet_ids = module.vpc.private_subnets
  security_group_ids = [aws_security_group.aws_vpce_tcp_secure_sg.id]

  tags = {
    Name      = "cw-logs-vpc-endpoint"
    Component = "VPCE"
  }
}

resource "aws_vpc_endpoint" "dynamodb" {
  count = local.enable_dynamodb_vpc_endpoint ? 1 : 0

  vpc_id            = local.main_vpc_id
  service_name      = "com.amazonaws.${var.bs_aws_region}.dynamodb"
  vpc_endpoint_type = "Gateway"

  route_table_ids = module.vpc.private_route_table_ids
  tags = {
    Name      = "dynamodb-vpc-endpoint"
    Component = "VPCE"
  }
}

resource "aws_vpc_endpoint" "s3" {
  count = local.enable_s3_vpc_endpoint ? 1 : 0

  vpc_id            = local.main_vpc_id
  service_name      = "com.amazonaws.${var.bs_aws_region}.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids = module.vpc.private_route_table_ids

  tags = {
    Name      = "s3-vpc-endpoint"
    Component = "VPCE"
  }
}

resource "aws_vpc_endpoint" "sqs" {
  count = local.enable_sqs_vpc_endpoint ? 1 : 0

  vpc_id              = local.main_vpc_id
  service_name        = "com.amazonaws.${var.bs_aws_region}.sqs"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true

  subnet_ids = module.vpc.private_subnets
  security_group_ids = [aws_security_group.aws_vpce_tcp_secure_sg.id]

  tags = {
    Name      = "sqs-vpc-endpoint"
    Component = "VPCE"
  }
}

resource "aws_vpc_endpoint" "ecr_api" {
  count = local.enable_ecr_vpc_endpoint ? 1 : 0

  vpc_id              = local.main_vpc_id
  service_name        = "com.amazonaws.${var.bs_aws_region}.ecr.api"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true

  subnet_ids = module.vpc.private_subnets
  security_group_ids = [aws_security_group.aws_vpce_tcp_secure_sg.id]

  tags = {
    Name      = "ecr-api-vpc-endpoint"
    Component = "VPCE"
  }
}

resource "aws_vpc_endpoint" "ecr_dkr" {
  count = local.enable_ecr_vpc_endpoint ? 1 : 0

  vpc_id              = local.main_vpc_id
  service_name        = "com.amazonaws.${var.bs_aws_region}.ecr.dkr"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true

  subnet_ids = module.vpc.private_subnets
  security_group_ids = [aws_security_group.aws_vpce_tcp_secure_sg.id]

  tags = {
    Name      = "ecr-dkr-vpc-endpoint"
    Component = "VPCE"
  }
}

resource "aws_security_group" "aws_vpce_tcp_secure_sg" {
  vpc_id = local.main_vpc_id
  name        = "aws_vpce_tcp_secure_sg"
  description = "Allow AWS Service connectivity via Interface Endpoints"

  ingress {
    from_port = 443
    to_port   = 443
    protocol  = "tcp"
    cidr_blocks = module.vpc.private_subnets_cidr_blocks
  }

  egress {
    from_port = 0
    to_port   = 0
    protocol = "-1" # Permitir todo o tráfego de saída
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Component = "VPCE"
  }
}
