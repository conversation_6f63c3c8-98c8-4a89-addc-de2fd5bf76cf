provider "aws" {
  region = var.bs_aws_region
  allowed_account_ids = [var.bs_aws_account_id]
}

module "cognito" {
  source = "../../modules/cognito"
  count  = var.bs_user_pool_arn_enabled ? 1 : 0

  user_pool_name                   = "${var.bs_tenant}-user-pool"
  user_pool_domain_name            = "auth-${var.bs_tenant}.${var.bs_web_domain}"
  user_pool_domain_certificate_arn = var.bs_web_domain_certificate_arn
  notification_email_sender_arn    = module.bill-payment-service[0].notification_email_sender_arn
  logout_urls = ["https://use-${var.bs_tenant}.${var.bs_web_domain}/"]
  environment                      = var.bs_env
  datadog_key_arn                  = aws_secretsmanager_secret.datadog.arn
  lambda_config_enabled            = true
  tenant_name                      = var.bs_tenant
}

module "bill-payment-service" {
  count = 1

  web_domain                 = var.bs_web_domain
  source                     = "../../modules/bill-payment-service"
  task_definitions_base_path = "${path.module}/task-definitions"
  environment                = var.bs_env
  aws_region                 = var.bs_aws_region
  account_id                 = var.bs_aws_account_id

  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id

  aws_ecs_bill_payment_cluster = aws_ecs_cluster.bill_payment_fargate_cluster

  tenant          = var.bs_tenant
  prefix          = var.bs_tenant
  certificate_arn = local.web_domain_certificate_arn
  datadog_key_arn = aws_secretsmanager_secret.datadog.arn

  config_files_bucket_arn = var.bs_config_files_bucket_arn

  app_environments              = "tenant,${var.bs_tenant},${local.env_prefix}${var.bs_tenant},friday"
  jazz_enabled                  = var.bs_jazz_enabled
  chatbot_enabled = var.bs_chatbot_enabled
  chatbot_adicional_secrets_map = var.********************************
  chatbot_adicional_tenants     = var.bs_chatbot_adicional_tenants

  tags = {
    Environment = var.bs_env
  }

  incoming_emails_sns_name = "${var.bs_tenant}-incoming-emails-${local.env_prefix}"
  incoming_emails_sqs_name = "${var.bs_tenant}-incoming-emails-${local.env_prefix}"

  ses_receiver_rule_name                = "bill_payment_ses_receiver-${var.bs_tenant}"
  email_domain = [var.bs_email_domain]
  ses_bucket_name                       = "${var.bs_aws_account_id}-${var.bs_tenant}-contas-received-emails"
  rendering_errors_to_s3_bucket_enabled = true
  quarantine_emails_bucket_name         = "${var.bs_aws_account_id}-${var.bs_tenant}-quarantine-emails-contas"
  notification_email_sender             = "notificacoes-${var.bs_tenant}@${var.bs_email_domain}"

  ses_unprocessed_emails_bucket_name                    = "${var.bs_aws_account_id}-${var.bs_tenant}-ses-unprocessed-emails-contas"
  ses_unprocessed_emails_bucket_replication_enabled     = false
  ses_unprocessed_emails_bucket_replication_rule_id     = null
  ses_unprocessed_emails_bucket_replication_destination = null
  ses_unprocessed_emails_bucket_replication_role        = null

  ses_incoming_emails_bucket_name                    = "${var.bs_aws_account_id}-${var.bs_tenant}-contas-incoming-emails"
  ses_incoming_emails_bucket_replication_enabled     = false
  ses_incoming_emails_bucket_replication_rule_id     = null
  ses_incoming_emails_bucket_replication_destination = null

  ses_incoming_emails_bucket_replication_role        = null
  user_pool_arn_enabled                              = var.bs_user_pool_arn_enabled
  user_pool_arn                                      = var.bs_user_pool_arn_enabled ? module.cognito[0].user_pool_arn : ""
  kms_key_arns                                       = var.bs_kms_key_arns
}

module "sns" {
  source              = "../../modules/sns"
  monthly_spend_limit = 20
}

resource "aws_ecs_cluster" "bill_payment_fargate_cluster" {
  name = "${var.bs_tenant}-bill-payment-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# resource "aws_secretsmanager_secret" "datadog_key" {
#   name = "friday/datadog"
# }

resource "aws_secretsmanager_secret" "datadog" {
  name = "${var.bs_tenant}/datadog"
}

resource "aws_secretsmanager_secret_version" "datadog_key" {
  version_stages = ["AWSCURRENT"]
  secret_id = aws_secretsmanager_secret.datadog.id
  secret_string = jsonencode({
    "DD_API_KEY" = coalesce(var.bs_datadog_key, "FIRST_VALUE")
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_ecr_pull_through_cache_rule" "rule" {
  ecr_repository_prefix = "ecr-public"
  upstream_registry_url = "public.ecr.aws"
}

resource "aws_iam_role" "lambda_exec" {
  name = "lambda_execution_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }

    ]
  })
}

resource "aws_iam_policy_attachment" "lambda_exec" {
  name = "lambda_execution_role_policy"
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"  # Basic execution role
  roles = [aws_iam_role.lambda_exec.name]
}

resource "aws_iam_policy" "lambda_sqs_policy" {
  name        = "lambda_sqs_policy"
  description = "Policy of lambda"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [

      {
        "Sid" : "",
        "Effect" : "Allow",
        "Action" : [
          "logs:PutLogEvents",
          "logs:CreateLogStream",
          "logs:CreateLogGroup"
        ],
        "Resource" : "*"
      },
      {
        "Sid" : "",
        "Effect" : "Allow",
        "Action" : [
          "sqs:*"
        ],
        "Resource" : "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_sqs_policy_attachment" {
  policy_arn = aws_iam_policy.lambda_sqs_policy.arn
  role       = aws_iam_role.lambda_exec.name
}