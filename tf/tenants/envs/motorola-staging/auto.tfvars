env = "staging"
tenant = "motorola"
web_domain = "meupagador.com.br"
backend_bucket_name = "tf-motorola-backend-infrastructure-staging"
dynamodb_table_name = "tf-motorola-backend-infrastructure-lock-table"

aws_region = "us-east-1"
aws_account_id = "************"
aws_azs = ["us-east-1a", "us-east-1b"]

user_pool_arn_enabled = false
use_self_signed_certificate = false

gitlab_env    = "motorola-stg"

#export TF_VAR_env="staging"
#export TF_VAR_tenant="motorola"
#export TF_VAR_web_domain="meupagador.com.br"
#export TF_VAR_backend_bucket_name="tf-motorola-backend-infrastructure-staging"
#export TF_VAR_dynamodb_table_name="tf-motorola-backend-infrastructure-lock-table"
#export TF_VAR_aws_region="us-east-1"
#export TF_VAR_aws_account_id="************"
#export TF_VAR_aws_azs="[\"us-east-1a\", \"us-east-1b\"]"
#export TF_VAR_user_pool_arn_enabled=false
#export TF_VAR_use_self_signed_certificate=false
#export TF_VAR_gitlab_env="motorola-stg"