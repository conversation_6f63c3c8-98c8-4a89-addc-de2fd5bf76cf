provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [var.aws_account_id]

  default_tags {
    tags = {
      Environment     = var.env
      ManagedBy       = "Terraform"
    }
  }
}

module "global" {  source = "../../modules/global" }

module "tfstate_backend" {
  source              = "../../modules/backend"
  environment         = var.env
  tenant              = var.tenant
  aws_region          = var.aws_region
  dynamodb_table_name = var.dynamodb_table_name
  backend_bucket_name = var.backend_bucket_name
}