locals {
  aws_account_id                       = data.aws_caller_identity.current.account_id
  public_subnets                       = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnets                      = ["10.0.101.0/24", "10.0.102.0/24"]
  database_subnets                     = ["10.0.151.0/24", "10.0.152.0/24"]
  ecs_sqs_list_policy_resource         = ["arn:aws:sqs:${var.aws_region}:${local.aws_account_id}:*"]
  config_files_bucket_name             = "${var.aws_account_id}-${var.tenant}-config-files"

  web_domain_certificate_arn = var.use_self_signed_certificate ? aws_acm_certificate.dummy.arn : aws_acm_certificate.web_domain.arn
}