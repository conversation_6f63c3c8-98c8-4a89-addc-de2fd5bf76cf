.DEFAULT_GOAL := default
ENVS := motorola-staging motorola-prod gigu-staging gigu-production

# Define targets e suas regras
TARGETS := check inspect init setup deploy destroy plan apply refresh import unlock env-plan env-apply replace
.PHONY := $(TARGETS)

ARGS := $(MAKECMDGOALS)

# Define o primeiro argumento
COMMAND := $(word 1, $(ARGS))

# Define o segundo argumento (se presente)
COMMAND_TENANT := $(word 2, $(ARGS))
TENANT := $(if $(TF_TENANT),$(TF_TENANT),$(COMMAND_TENANT))

# Verifica se o target existe
define check-target
    $(if $(findstring $(1),$(TARGETS)),,$(error Target >>> $(1) <<< não existe))
endef

define check-env
	$(if $(findstring $(TENANT),$(ENVS)),,$(error Target >>> $(TENANT) <<< não existe))
endef

check:
	$(call check-target,$(COMMAND))
	$(call check-env,$(TENANT))

destroy:
	@echo "Destroying $(TENANT)"
	terraform destroy -var-file=envs/$(TENANT)/auto.tfvars -lock=false -auto-approve

setup:
	@echo "Setting up $(TENANT)"
	terraform -chdir=envs init  -var-file=$(TENANT)/auto.tfvars
	terraform -chdir=envs apply -var-file=$(TENANT)/auto.tfvars -auto-approve

init:
	terraform workspace select $(TENANT)
	terraform init -reconfigure -backend-config=envs/$(TENANT)/config.s3.tfbackend -var-file=envs/$(TENANT)/auto.tfvars

upgrade:
	terraform init -upgrade -backend-config=envs/$(TENANT)/config.s3.tfbackend -var-file=envs/$(TENANT)/auto.tfvars

import:
	terraform import -var-file=envs/$(TENANT)/auto.tfvars $(word 3, $(ARGS)) $(word 4, $(ARGS))

deploy:
	terraform init -backend-config=envs/$(TENANT)/config.s3.tfbackend -var-file=envs/$(TENANT)/auto.tfvars
	terraform apply -var-file=envs/$(TENANT)/auto.tfvars -auto-approve

replace:
	terraform apply -var-file=envs/$(TENANT)/auto.tfvars -auto-approve -replace="$(word 2, $(ARGS))"

unlock:
	terraform force-unlock -force $(word 2, $(ARGS))

# ENV

env-init:
	@echo "Init $(TENANT)"
	terraform -chdir=envs init -var-file=$(TENANT)/auto.tfvars

env-upgrade:
	@echo "Init and upgrade $(TENANT)"
	terraform -chdir=envs init -upgrade -var-file=$(TENANT)/auto.tfvars

env-plan:
	@echo "Planning env $(TENANT)"
	terraform -chdir=envs plan -var-file=$(TENANT)/auto.tfvars

env-apply:
	@echo "Applying env $(TENANT)"
	terraform -chdir=envs apply -var-file=$(TENANT)/auto.tfvars

# CI

ci-init:
	terraform workspace select $(TENANT)-ci
	@echo "Init $(TENANT)"
	terraform -chdir=ci init -reconfigure -backend-config="../envs/$(TENANT)/config.s3.tfbackend" -var-file="../envs/$(TENANT)/auto.tfvars"

ci-secrets:
	terraform workspace select $(TENANT)-ci
	@echo "Applying secrets upsert for $(TENANT)"
	terraform -chdir=ci apply -var-file="../envs/$(TENANT)/auto.tfvars" -target="aws_ssm_parameter.gitlab_group_token"

ci-upgrade:
	terraform workspace select $(TENANT)-ci
	@echo "Init and upgrade $(TENANT)"
	terraform -chdir=ci init -upgrade -var-file="../envs/$(TENANT)/auto.tfvars"

ci-plan:
	terraform workspace select $(TENANT)-ci
	@echo "Planning env $(TENANT)"
	terraform -chdir=ci plan -var-file="../envs/$(TENANT)/auto.tfvars"

ci-apply:
	terraform workspace select $(TENANT)-ci
	@echo "Applying env $(TENANT)"
	terraform -chdir=ci apply -var-file="../envs/$(TENANT)/auto.tfvars"

ci-destroy:
	terraform workspace select $(TENANT)-ci
	@echo "Destroying $(TENANT)"
	terraform -chdir=ci destroy -var-file="../envs/$(TENANT)/auto.tfvars"

# OTHERWISE

%:
	terraform workspace select $(TENANT)
	$(call check-target,$(COMMAND))
	terraform $(COMMAND) -var-file=envs/$(TENANT)/auto.tfvars