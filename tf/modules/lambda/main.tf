locals {
  environment_map = var.environment_variables == null ? [] : [var.environment_variables]
}

resource "aws_s3_bucket" "lambda-bucket" {
  bucket = var.lambda_s3_bucket
  #key    = "${var.lambda_version}/bill-payment-service.jar"

  # Enable versioning so we can see the full revision history of our
  # state files
  versioning {
    enabled = true
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

data "aws_iam_policy_document" "lambda_iam_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      identifiers = ["lambda.amazonaws.com"]
      type        = "Service"
    }
    effect = "Allow"
    sid    = ""
  }
}

resource "aws_iam_role" "lambda-iam-role" {
  name = var.lambda_iam_role_name

  assume_role_policy = data.aws_iam_policy_document.lambda_iam_role_policy.json
}

data "aws_iam_policy_document" "dynamo-policy-doc" {
  statement {
    sid       = "AllowDynamoPermissions"
    effect    = "Allow"
    resources = var.lambda_dynamo_policy_resource
    actions   = ["dynamodb:*"]
  }
}

resource "aws_iam_policy" "dynamo-policy" {
  count  = var.dynamo_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.dynamo-policy-doc.json
}

resource "aws_iam_role_policy_attachment" "lamba_exec_role_dynamo" {
  depends_on = [aws_iam_role.lambda-iam-role]
  count      = var.dynamo_access_enabled ? 1 : 0
  role       = var.lambda_iam_role_name
  policy_arn = aws_iam_policy.dynamo-policy[0].arn
}

resource "aws_iam_role_policy_attachment" "lamba_exec_role_eni" {//
  depends_on = [aws_iam_role.lambda-iam-role]
  role       = var.lambda_iam_role_name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy_attachment" "lamba_exec_role_dynamodb" {
  depends_on = [aws_iam_role.lambda-iam-role]
  count      = var.dynamo_access_enabled ? 1 : 0
  role       = var.lambda_iam_role_name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaDynamoDBExecutionRole"
}

resource "aws_iam_role_policy_attachment" "lamba_exec_role_xray" {//
  depends_on = [aws_iam_role.lambda-iam-role]
  count      = var.xray_tracing_enabled ? 1 : 0
  role       = var.lambda_iam_role_name
  policy_arn = "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess"
}

resource "aws_lambda_function" "lambda" {
  depends_on    = [aws_iam_role_policy_attachment.lamba_exec_role_eni]
  function_name = var.lambda_name

  filename      = var.lambda_filename
  #s3_bucket = var.lambda_s3_bucket
  #s3_key    = join("/", [var.lambda_version, var.lambda_filename])

  handler     = var.lambda_handler
  runtime     = var.lambda_runtime
  memory_size = var.lambda_memory_size
  timeout     = var.lambda_timeout

  role = aws_iam_role.lambda-iam-role.arn

  tracing_config {
    mode = var.xray_tracing_enabled ? "Active" : "PassThrough"
  }

  tags = {
    Environment = var.environment
  }

  dynamic "environment" {
    for_each = local.environment_map
    content {
      variables = environment.value
    }
  }
}

resource "aws_lambda_permission" "allow_s3_invoke" {
  count         = var.s3_invoke_lambda ? 1 : 0
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "s3.amazonaws.com"
  source_arn    = var.s3_bucket_arn
}

resource "aws_s3_bucket_notification" "allow_s3_invoke" {
  depends_on = [aws_lambda_permission.allow_s3_invoke]
  count  = var.s3_invoke_lambda ? 1 : 0
  bucket = var.s3_bucket_id
  lambda_function {
    lambda_function_arn = aws_lambda_function.lambda.arn
    events              = var.s3_invocation_events
  }
}

data "aws_iam_policy_document" "get_object" {
  statement {
    effect    = "Allow"
    actions   = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetObjectTagging",
      "s3:DeleteObject",
    ]
    resources = [
      "${aws_s3_bucket.lambda-bucket.arn}/*"
    ]
  }
  statement {
    effect    = "Allow"
    actions   = [
      "s3:ListBucket"
    ]
    resources = [
      "${aws_s3_bucket.lambda-bucket.arn}"
    ]
  }
}

resource "aws_iam_policy" "get_object" {
  count  = var.s3_read_write_objects ? 1 : 0
  policy = data.aws_iam_policy_document.get_object.json
}

resource "aws_iam_role_policy_attachment" "get_object" {
  depends_on = [aws_iam_role.lambda-iam-role]
  count      = var.s3_read_write_objects ? 1 : 0
  role       = var.lambda_iam_role_name
  policy_arn = aws_iam_policy.get_object[0].arn
}

data "aws_iam_policy_document" "send_email" {
  statement {
    effect    = "Allow"
    actions   = [
      "ses:SendEmail",
      "ses:SendRawEmail",
      "ses:SendTemplatedEmail"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "send_email" {
  count  = var.send_email ? 1 : 0
  policy = data.aws_iam_policy_document.send_email.json
}

resource "aws_iam_role_policy_attachment" "send_email" {
  depends_on = [aws_iam_role.lambda-iam-role]
  count      = var.send_email ? 1 : 0
  role       = var.lambda_iam_role_name
  policy_arn = aws_iam_policy.send_email[0].arn
}
