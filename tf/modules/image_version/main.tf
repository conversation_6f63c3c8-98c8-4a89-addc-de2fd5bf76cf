data "external" "service_image_tag" {
  program = [
    "bash",
    "-c",
    <<EOT
      IMAGE_TAG=$(aws ecs describe-task-definition --task-definition=${var.task_name} | grep -Eo '(${var.ecr_repository_name}:[0-9a-z-]+)' | head -1 | cut -f2 -d':' || echo 'latest')
      echo "{\"image_tag\": \"$IMAGE_TAG\"}"
    EOT
  ]
}

output image_tag {
  value = data.external.service_image_tag.result["image_tag"]
}