locals {
  cluster_name        = var.aws_ecs_bill_payment_cluster.name
  service_name        = "ai-chatbot"
  container_name      = "AIChatbot"
  ecr_repository_name = "ai-chatbot"
  app_port            = 8443
}

module "friday_ai_chatbot_task" {
  source = "../../modules/fargate_task"

  tenant                = var.tenant
  app_port              = local.app_port
  service_name          = local.service_name
  container_name        = local.container_name
  cluster_name          = local.cluster_name
  account_id            = var.account_id
  environment           = var.environment
  app_environments      = var.app_environments
  prefix                = local.service_name
  ecr_repository_name   = local.ecr_repository_name
  fargate_cpu           = 1024
  fargate_memory        = 4096
  task_definition       = var.task_definition
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.chatbot_history.arn,
    "${aws_dynamodb_table.chatbot_history.arn}/*",
    var.billpayment_table_arn,
    "${var.billpayment_table_arn}/*",
    var.shedlock_table_arn,
    "${var.shedlock_table_arn}/*",
  ]
  s3_read_objects = true
  s3_bucket_arns = [
    "arn:aws:s3:::${var.account_id}-${var.tenant}-config-files",
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = var.ecs_sqs_list_policy_resource
  sns_access_enabled      = false
  ecs_sns_policy_resource = []
  secrets_enabled         = true
  secrets_arns = [
    var.open_ai_key_secret_arn,
    var.blip_password_secret_arn,
    var.datadog_secret_arn,
    var.waba_token_arn,
  ]
  user_pool_arn_enabled = false
  kms_enabled           = false
  kms_key_arns = []
  aws_region            = var.aws_region
  send_email            = true
  tags                  = var.tags
  secrets_map = merge({
    "OPENAI_TOKEN"                               = var.open_ai_key_secret_arn
    "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH" = var.blip_password_secret_arn
    "DD_API_KEY"                                 = "${var.datadog_secret_arn}:DD_API_KEY::"
    "INTEGRATIONS_WHATSAPP_API_TOKEN"            = var.waba_token_arn
  }, { for key, value in data.aws_secretsmanager_secret.adicional_secret : key => value.arn })
}

data "aws_secretsmanager_secret" "adicional_secret" {
  for_each = var.chatbot_adicional_secrets_map
  name     = each.value
}

module "ai_chatbot_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = var.aws_ecs_bill_payment_cluster
  aws_private_subnet_id   = var.private_subnets
  aws_public_subnet_id    = var.public_subnets
  aws_vpc_id              = var.vpc_id
  load_balance_enabled    = true
  prefix                  = local.service_name
  container_name          = local.container_name
  app_port                = local.app_port
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.friday_ai_chatbot_task
  certificate_arn_enabled = true
  certificate_arn         = var.certificate_arn
  app_protocol            = "HTTPS"
}