variable "environment" {
  description = "The environment"
}

variable "aws_region" {
  description = "The AWS Region"
}

variable "task_definition" {
  description = "Task Definition file"
}

variable "vpc_id" {
  description = "vpc_id"
}

variable "private_subnets" {
  description = "private_subnets"
}

variable "public_subnets" {
  description = "public_subnets"
}

variable "certificate_arn" {
  description = "certificate_arn"
}

variable "ecs_sqs_list_policy_resource" {
  description = "ecs_sqs_list_policy_resource"
}

variable "shedlock_table_arn" {
  description = "shedlock_table_arn"
}

variable "billpayment_table_arn" {
  description = "billpayment_table_arn"
}

variable "blip_password_secret_arn" {
  description = "billpayment_table_arn"
}

variable "waba_token_arn" {
  description = "waba token"
  type        = string
}

variable "open_ai_key_secret_arn" {
  description = "open_ai_key_secret_arn"
}

variable "datadog_secret_arn" {
  description = "datadog_secret_arn"
}

variable "account_id" {
  description = "The AWS Account ID"
}

variable "aws_ecs_bill_payment_cluster" {}

variable "app_environments" {}

variable "tags" {}

variable "chatbot_adicional_secrets_map" {
  type = map(string)
}

variable "tenant" {
  type = string
}