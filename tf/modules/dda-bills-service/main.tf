locals {
  service_name        = "${var.prefix}-dda-bills"
  container_name      = "dda-bills-serviceAPI"
  ecr_repository_name = "dda-bills-service-api"
  app_port            = 8443
}

module "fargate_service" {
  source                = "../fargate_service"
  aws_ecs_cluster       = var.aws_ecs_cluster
  aws_private_subnet_id = var.aws_private_subnet_id
  aws_public_subnet_id  = var.aws_public_subnet_id
  aws_vpc_id            = var.aws_vpc_id
  prefix                = local.service_name
  container_name        = local.container_name
  app_port              = local.app_port
  app_count             = 0
  app_protocol          = "HTTPS"
  health_check_path     = "/health"
  task_definition       = module.fargate_task
  certificate_arn       = var.certificate_arn
}

module "fargate_task" {
  source       = "../fargate_task"
  prefix       = local.service_name
  service_name = "${local.service_name}-service"
  tenant       = var.tenant

  ecr_repository_name = local.ecr_repository_name
  fargate_cpu         = 256
  fargate_memory      = 1024
  other_container_definitions = {
    cpu    = var.environment == "production" ? 246 : 246
    memory = var.environment == "production" ? 768 : 768
  }
  task_definition            = var.task_definition
  dynamo_access_enabled      = var.dynamo_access_enabled
  ecs_dynamo_policy_resource = var.ecs_dynamo_policy_resource
  sqs_access_enabled         = var.sqs_access_enabled
  ecs_sqs_policy_resource    = var.ecs_sqs_policy_resource

  sns_access_enabled      = var.sns_access_enabled
  ecs_sns_policy_resource = var.sns_topic_arns
  s3_read_objects         = true
  s3_bucket_arns = [
    module.dda_files_lambda.aws_lambda_bucket_arn,
    "arn:aws:s3:::${var.account_id}-${var.tenant}-config-files",
  ]
  secrets_enabled = true
  secrets_arns    = var.secrets_arns

  cluster_name     = var.aws_ecs_cluster.name
  container_name   = local.container_name
  app_environments = var.app_environments
  environment      = var.environment
  account_id       = var.account_id
  tags             = var.tags
  app_port         = local.app_port
}

module "dda_files_lambda" {
  account_id            = var.account_id
  source                = "./modules/dda-bills-lambda"
  environment           = var.environment
  lambda_runtime        = "python3.9"
  lambda_filename       = "${path.module}/../../files/dda-files-lambda.zip"
  lambda_handler        = "lambda_function.lambda_handler"
  lambda_iam_role_name  = "dda-files-lambda-role"
  lambda_name           = "dda-files"
  s3_read_write_objects = true
  lambda_version        = "1"
  lambda_memory_size    = 1024
  lambda_timeout        = 900
}