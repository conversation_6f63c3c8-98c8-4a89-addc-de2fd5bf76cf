variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_private_subnet_id" {}

variable "aws_public_subnet_id" {}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
  default     = 8080
}

variable "app_count" {
  description = "Number of docker containers to run"
  default     = 2
}

variable "health_check_path" {
  default = "/"
}

variable "fargate_cpu" {
  description = "Fargate instance CPU units to provision (1 vCPU = 1024 CPU units)"
  default     = "256"
}

variable "fargate_memory" {
  description = "Fargate instance memory to provision (in MiB)"
  default     = "512"
}

variable "task_definition" {
  description = "path to task definition json"
}

variable "certificate_arn" {
  description = "arn of the certificate to be used on the ALB"
}

variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "secrets_arns" {
  default = []
  type    = list(string)
}

variable "environment" {
  description = "The environment"
}
variable "app_environments" {
  type = string
}

variable "tags" {
  description = "Tags to use on project components"
  type = map
}

variable "ecs_sqs_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sns_topic_arns" {
  description = "ECS policy resources for sns integration"
  default     = []
}

variable "sqs_access_enabled" {
  default = false
}

variable "sns_access_enabled" {
  default = false
}

variable "account_id" {
  description = "The AWS account ID"
}

variable "aws_ecs_cluster" {
    description = "The ECS cluster"
}

variable "prefix"{
  type = string
}

variable "ecs_dynamo_policy_resource" {
  description = "ECS policy resources for dynamo integration"
  default     = []
  type = list(string)
}

variable "tenant" {
  type = string
}