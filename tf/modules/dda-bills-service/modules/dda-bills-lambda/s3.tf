resource "aws_s3_bucket" "lambda-bucket" {
  bucket = "${var.account_id}-dda-files-lambda"
  #key    = "${var.lambda_version}/bill-payment-service.jar"

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "example" {
  bucket = aws_s3_bucket.lambda-bucket.id

  rule {
    id = "intelligent-tiering"

    transition {
      days          = 0
      storage_class = "INTELLIGENT_TIERING"
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_intelligent_tiering_configuration" "dda-files-tiering" {
  bucket = aws_s3_bucket.lambda-bucket.id
  name   = "EntireBucket"
  status = "Enabled"

  tiering {
    access_tier = "DEEP_ARCHIVE_ACCESS"
    days        = 365
  }
}