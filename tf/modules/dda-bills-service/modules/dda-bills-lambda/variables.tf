variable "lambda_name" {
  description = "The name of the lambda function"
}

variable "account_id" {
  description = "The AWS account ID"
}

variable "lambda_filename" {
  description = "The filename for the lambda function"
}

variable "lambda_handler" {
  description = "The handler for the lambda function"
}

variable "lambda_runtime" {
  description = "The handler for the lambda function"
  default     = "java8"
}

variable "lambda_memory_size" {
  description = "The handler for the lambda function"
  default     = 256
}

variable "lambda_timeout" {
  description = "The handler for the lambda function"
  default     = 15
}

variable "s3_read_write_objects" {
  description = "Permission to read and write the s3_bucket"
  default = false
}

variable "lambda_iam_role_name" {
  description = "The name of the role to be added to the lambda function"
}

variable "lambda_version" {
  description = "The version of the lambda function to be deployed"
}

variable "lambda_deployment_execution_arn" {
  description = "The name arn of lambda deployment to be used"
  default = ""
}

variable "lambda_dynamo_policy_resource" {
  description = "The resources where the policy will be applied"
  default = []
}

variable "environment" {
  description = "The environment"
}

variable "s3_bucket_id" {
  description = "S3 bucket id that will invoke lambda"
  default     = ""
}

variable "s3_bucket_arn" {
  description = "S3 bucket arn that will invoke lambda"
  default     = ""
}

variable "s3_invocation_events" {
  description = "List of events that will invoke lambda"
  default     = ""
}
