locals {
  environment_map = { motorola_FTP_ROOT_FOLDER = "/Prod/DDAFiles_34701685000115"
    motorola_BUCKET_NAME = aws_s3_bucket.lambda-bucket.bucket
    FTP_ROOT_FOLDER   = "/Prod/DDAFiles_34701685000115" # TODO PARAMETERIZE FTP ROOT FOLDER
    TZ                = "Brazil/East"
    BUCKET_NAME       = aws_s3_bucket.lambda-bucket.bucket
  }
}

resource "aws_secretsmanager_secret" "arbi_ftp" {
  name = "friday/arbi-ftp"
}

resource "aws_secretsmanager_secret" "motorola_arbi_ftp" {
  name = "motorola/arbi-ftp"
}

resource "aws_secretsmanager_secret_version" "motorola_arbi_ftp" {
  secret_id     = aws_secretsmanager_secret.motorola_arbi_ftp.id
  secret_string = jsonencode(
    {
      "host"     = ""
      "port"     = ""
      "username" = ""
      "password" = ""
    }
  )

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}


resource "aws_secretsmanager_secret_version" "arbi_ftp" {
  secret_id     = aws_secretsmanager_secret.arbi_ftp.id
  secret_string = jsonencode(
    {
      "host"     = ""
      "port"     = ""
      "username" = ""
      "password" = ""
    }
  )

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

data "aws_iam_policy_document" "lambda_iam_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      identifiers = ["lambda.amazonaws.com"]
      type        = "Service"
    }
    effect = "Allow"
    sid    = ""
  }
}

resource "aws_iam_role_policy" "lambda_secrets_policy" {
  name = "lambda-secrets-policy"
  role = aws_iam_role.lambda-iam-role.name

  policy = data.aws_iam_policy_document.lambda_secrets_policy.json
}

data "aws_iam_policy_document" "lambda_secrets_policy" {
  statement {
    sid       = "ReadFtpSecrets"
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [aws_secretsmanager_secret.arbi_ftp.arn, aws_secretsmanager_secret.motorola_arbi_ftp.arn]
  }
}

resource "aws_iam_role" "lambda-iam-role" {
  name = var.lambda_iam_role_name

  assume_role_policy = data.aws_iam_policy_document.lambda_iam_role_policy.json
}

resource "aws_iam_role_policy_attachment" "lamba_exec_role_eni" {//
  depends_on = [aws_iam_role.lambda-iam-role]
  role       = var.lambda_iam_role_name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_lambda_function" "lambda" {
  depends_on    = [aws_iam_role_policy_attachment.lamba_exec_role_eni]
  function_name = var.lambda_name

  filename      = var.lambda_filename
  #s3_bucket = var.lambda_s3_bucket
  #s3_key    = join("/", [var.lambda_version, var.lambda_filename])

  handler     = var.lambda_handler
  runtime     = var.lambda_runtime
  memory_size = var.lambda_memory_size
  timeout     = var.lambda_timeout

  role = aws_iam_role.lambda-iam-role.arn

  tracing_config {
    mode = "PassThrough"
  }

  environment {
    variables = local.environment_map
  }

  tags = {
    Environment = var.environment
  }
}

data "aws_iam_policy_document" "get_object" {
  statement {
    effect    = "Allow"
    actions   = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetObjectTagging",
      "s3:DeleteObject",
    ]
    resources = [
      "${aws_s3_bucket.lambda-bucket.arn}/*"
    ]
  }
  statement {
    effect    = "Allow"
    actions   = [
      "s3:ListBucket"
    ]
    resources = [
      "${aws_s3_bucket.lambda-bucket.arn}"
    ]
  }
}

resource "aws_iam_policy" "get_object" {
  count  = var.s3_read_write_objects ? 1 : 0
  policy = data.aws_iam_policy_document.get_object.json
}

resource "aws_iam_role_policy_attachment" "get_object" {
  depends_on = [aws_iam_role.lambda-iam-role]
  count      = var.s3_read_write_objects ? 1 : 0
  role       = var.lambda_iam_role_name
  policy_arn = aws_iam_policy.get_object[0].arn
}