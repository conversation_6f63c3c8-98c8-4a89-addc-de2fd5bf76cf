variable "ecs_task_execution_role_name" {
  description = "ECS task execution role name"
  default = "myEcsTaskExecutionRole"
}

variable "secrets_enabled" {
  default = false
}

variable "secrets_arns" {
  default = []
  type = list(string)
}

variable "task_name" {
  description = "ECS task name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}