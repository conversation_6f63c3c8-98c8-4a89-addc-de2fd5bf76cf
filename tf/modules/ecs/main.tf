# ALB Security group
# This is the group you need to edit if you want to restrict access to your application
resource "aws_security_group" "lb" {
  name        = "group-ecs-alb"
  description = "controls access to the ALB"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 8080
    to_port     = 8080
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"] # add a CIDR block here
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Traffic to the ECS Cluster should only come from the ALB
resource "aws_security_group" "ecs_tasks" {
  name        = "group-ecs-tasks"
  description = "allow inbound access from the ALB only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.app_port
    to_port         = var.app_port
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_alb" "main" {
  name            = var.load_balance_name
  subnets         = var.aws_public_subnet_id
  security_groups = [aws_security_group.lb.id]
  access_logs {
    bucket  = var.access_logs_bucket
    enabled = var.access_logs_enabled
  }

}

resource "aws_alb_target_group" "app" {
  name                 = var.alb_target_group_name
  port                 = var.app_port
  protocol             = "HTTPS"
  vpc_id               = var.aws_vpc_id
  target_type          = "ip"
  deregistration_delay = 120

  health_check {
    healthy_threshold   = "3"
    unhealthy_threshold = "6"
    interval            = "60"
    protocol            = "HTTPS"
    matcher             = "200-499"
    timeout             = "50"
    path                = var.health_check_path
  }
}

# Redirect all traffic from the ALB to the target group
resource "aws_alb_listener" "front_end" {
  load_balancer_arn = aws_alb.main.id
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  default_action {
    target_group_arn = aws_alb_target_group.app.id
    type             = "forward"
  }
}

resource "aws_lb_listener_certificate" "friday_certificate" {
  count = var.alternative_certificate_arn_enabled  ? 1 : 0
  listener_arn    = aws_alb_listener.front_end.arn
  certificate_arn = var.alternative_certificate_arn
}

resource "aws_ecs_cluster" "main" {
  name = var.cluster_name
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

module "service_image_version" {
  source         = "../image_version"
  container_name = var.container_name
  cluster_name   = var.cluster_name
  service_name   = var.service_name
}

resource "aws_ecs_task_definition" "app" {
  family                   = var.task_name
  execution_role_arn       = module.execution_role.arn
  task_role_arn            = module.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.fargate_cpu
  memory                   = var.fargate_memory
  container_definitions    = templatefile(var.task_definition, {
    app_image                 = "${aws_ecr_repository.default.repository_url}:${module.service_image_version.image_tag}"
    app_port                  = var.app_port
    container_name            = var.container_name
    service_name              = "liveness-gateway"
    secret_arns               = var.secrets_arns
    environment               = var.environment
  })
  tags = var.tags
}

resource "aws_ecs_service" "main" {
  name             = var.service_name
  cluster          = aws_ecs_cluster.main.id
  task_definition  = aws_ecs_task_definition.app.arn
  desired_count    = var.app_count
  launch_type      = "FARGATE"
  platform_version = "1.4.0"

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = var.aws_private_subnet_id
    assign_public_ip = true
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.app.id
    container_name   = "BillPaymentAPI"
    container_port   = var.app_port
  }

  depends_on = [aws_alb_listener.front_end, module.execution_role.role_policy_attachment_execution_role]
}

# Duplicado??
# module "bill_events_dlq" {
#   source  = "terraform-aws-modules/sqs/aws"
#   version = "~> 2.0"
#   name    = "bill_events_dlq"
# }
