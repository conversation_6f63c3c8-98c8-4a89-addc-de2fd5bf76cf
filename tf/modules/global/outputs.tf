output "runner_pipeline_policy_arn_parameter_name" { value = "/tf/gitlab_runner_ci_pipeline_policy_arn" }

output "runner_private_subnet_ids_parameter_name" { value = "/tf/gitlab_runner_private_subnet_ids" }

output "runner_vpc_id_parameter_name" { value = "/tf/gitlab_runner_vpc_id" }

output "runner_gitlab_token" { value = "/tf/gitlab_runner_token" }

output "runner_gitlab_group_token" { value = "/tf/gitlab_runner_group_token" }

output "notification_templates_cf_distribution_id_parameter_name" {
  value = "/tf/notification_templates_cloudfront_distribution_id"
}

output "webapp_cf_distribution_id_parameter_name" {
  value = "/tf/webapp_cloudfront_distribution_id"
}

output "web_domain_certificate_arn_parameter_name" { value = "/tf/web_domain_certificate_arn" }

output "component_ci" { value = "CI" }

output "component_tf_state" { value = "TerraformState" }