# resource "aws_ses_template" "wallet_invite_assistant_without_account_new" {
#   name = "wallet_invite_assistant_without_account__4_13_0"
#   subject = "<PERSON><PERSON><PERSON>, {{memberName}}. {{founderName}} quer a sua ajuda para cuidar das contas com Me Poupe!"
#   html    = file("${path.module}/email-templates/wallet-invite-assistant-without-account.html")
#   text    = file("${path.module}/email-templates/wallet-invite-assistant-without-account.txt")
# }
#
# resource "aws_ses_template" "wallet_invite_collaborator_without_account_new" {
#   name = "wallet_invite_collaborator_without_account__4_13_0"
#   subject = "Ol<PERSON>, {{memberName}}. {{founderName}} tem um convite para você"
#   html    = file("${path.module}/email-templates/wallet-invite-collaborator-without-account.html")
#   text    = file("${path.module}/email-templates/wallet-invite-collaborator-without-account.txt")
# }
#
# resource "aws_ses_template" "wallet_invite_reminder_collaborator_without_account" {
#   name = "wallet_invite_reminder_collaborator_without_account__4_13_0"
#   subject = "<PERSON><PERSON><PERSON>, {{memberName}}. {{founderName}} tem um convite para você"
#   html    = file("${path.module}/email-templates/wallet-invite-reminder-collaborator-without-account.html")
#   text    = file("${path.module}/email-templates/wallet-invite-reminder-collaborator-without-account.txt")
# }
#
# resource "aws_ses_template" "wallet_invite_reminder_assistant_without_account" {
#   name = "wallet_invite_reminder_assistant_without_account__4_13_0"
#   subject = "Olá, {{memberName}}. {{founderName}} quer a sua ajuda para cuidar das contas com Me Poupe"
#   html    = file("${path.module}/email-templates/wallet-invite-reminder-assistant-without-account.html")
#   text    = file("${path.module}/email-templates/wallet-invite-reminder-assistant-without-account.txt")
# }
