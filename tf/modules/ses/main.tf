locals {
  bounce_suffix      = "-bounce"
  bounce_bucket_name = format("%s%s", var.ses_bucket_name, local.bounce_suffix)
}

module "email_templates" {
  source = "./modules/templates"
}

module "received_emails" {
  source      = "./modules/bucket"
  bucket_name = var.ses_bucket_name
  region      = var.region
  has_policy  = true
  policy_json = data.aws_iam_policy_document.ses.json
}

data "aws_iam_policy_document" "ses" {
  statement {
    sid = "AllowSESToWriteIntoReportsBucket"
    principals {
      type        = "Service"
      identifiers = [
        "ses.amazonaws.com"
      ]
    }
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::${var.ses_bucket_name}/*"
    ]
  }
}

module "incoming_emails" {
  source                         = "./modules/bucket"
  bucket_name                    = var.ses_incoming_emails_bucket_name
  replication_enabled            = var.ses_incoming_emails_bucket_replication_enabled
  replication_rule_id            = var.ses_incoming_emails_bucket_replication_rule_id
  replication_bucket_destination = var.ses_incoming_emails_bucket_replication_destination
  replication_role               = var.ses_incoming_emails_bucket_replication_role
  region                         = var.region
  has_policy                     = true
  policy_json                    = data.aws_iam_policy_document.incoming_emails.json
}

data "aws_iam_policy_document" "incoming_emails" {
  statement {
    sid = "AllowSESToWriteIntoReportsBucket"
    principals {
      type        = "Service"
      identifiers = [
        "ses.amazonaws.com"
      ]
    }
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::${var.ses_incoming_emails_bucket_name}/*"
    ]
  }
}

module "unprocessed_emails" {
  source                         = "./modules/bucket"
  bucket_name                    = var.ses_unprocessed_emails_bucket_name
  replication_enabled            = var.ses_unprocessed_emails_bucket_replication_enabled
  replication_rule_id            = var.ses_unprocessed_emails_bucket_replication_rule_id
  replication_bucket_destination = var.ses_unprocessed_emails_bucket_replication_destination
  replication_role               = var.ses_unprocessed_emails_bucket_replication_role
  region                         = var.region
}

module "quarantine_emails" {
  source      = "./modules/bucket"
  bucket_name = var.quarantine_emails_bucket_name
  region      = var.region
}

module "bounce" {
  source      = "./modules/bucket"
  bucket_name = local.bounce_bucket_name
  region      = var.region
  has_policy  = true
  policy_json = data.aws_iam_policy_document.bounce.json
}

data "aws_iam_policy_document" "bounce" {
  statement {
    sid = "AllowSESToWriteIntoReportsBucket"
    principals {
      type        = "Service"
      identifiers = [
        "ses.amazonaws.com"
      ]
    }
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::${local.bounce_bucket_name}/*"
    ]
  }
}

module "error_rendering_to_s3" {
  source        = "./modules/bucket"
  bucket_name   = var.rendering_bucket_name
  region        = var.region
  create_bucket = var.rendering_errors_to_s3_bucket_enabled
  has_policy    = true
  policy_json   = data.aws_iam_policy_document.error_rendering_to_s3.json
}

data "aws_iam_policy_document" "error_rendering_to_s3" {

  statement {
    sid = "AllowSESToWriteIntoReportsBucket"
    principals {
      type        = "Service"
      identifiers = [
        "ses.amazonaws.com"
      ]
    }
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::${var.rendering_bucket_name}/*"
    ]
  }
}

resource "aws_ses_receipt_rule_set" "default" {
  rule_set_name = "${var.tenant}-rule-set"
}

resource "aws_ses_active_receipt_rule_set" "default" {
  rule_set_name = "${var.tenant}-rule-set"
  depends_on    = [aws_ses_receipt_rule_set.default]
}

resource "aws_ses_receipt_rule" "default" {
  name          = var.ses_receiver_rule_name
  rule_set_name = aws_ses_receipt_rule_set.default.rule_set_name
  recipients    = var.email_domain
  enabled       = true
  tls_policy    = "Optional"
  scan_enabled  = var.scan_enabled

  s3_action {
    bucket_name = var.ses_incoming_emails_bucket_name
    position    = 1
    topic_arn   = var.sns_receiver_emails_arn
  }

  stop_action {
    position = 2
    scope    = "RuleSet"
  }

  depends_on = [module.received_emails]
}

resource "aws_ses_receipt_rule" "bounce" {
  name          = format("%s%s", var.ses_receiver_rule_name, local.bounce_suffix)
  rule_set_name = aws_ses_receipt_rule_set.default.rule_set_name
  recipients    = ["bounce@${element(var.email_domain,0)}"]
  enabled       = true
  tls_policy    = "Require"
  scan_enabled  = var.scan_enabled

  s3_action {
    bucket_name = format("%s%s", var.ses_bucket_name, local.bounce_suffix)
    position    = 1
  }

  stop_action {
    position = 2
    scope    = "RuleSet"
  }

  depends_on = [module.bounce]
}

resource "aws_ses_receipt_rule" "email_forwarder" {
  name          = "email_forwarder"
  rule_set_name = aws_ses_receipt_rule_set.default.rule_set_name
  recipients    = ["atendimento@${element(var.email_domain,0)}", "cadastro@${element(var.email_domain,0)}"]
  enabled       = true
  tls_policy    = "Optional"
  scan_enabled  = var.scan_enabled

  workmail_action {
    //FIXME: https://github.com/terraform-providers/terraform-provider-aws/issues/6430
    organization_arn = "arn:aws:workmail:us-east-1:381563809177:organization/m-aa4956bc3e794544958ab46dec014555"
    position         = 1
  }

  stop_action {
    position = 2
    scope    = "RuleSet"
  }
}

resource "aws_ses_receipt_rule" "notifications" {
  name          = "notifications"
  rule_set_name = aws_ses_receipt_rule_set.default.rule_set_name
  recipients    = ["notificacoes@${element(var.email_domain,0)}"]
  enabled       = true
  tls_policy    = "Optional"
  scan_enabled  = var.scan_enabled

  stop_action {
    position = 1
    scope    = "RuleSet"
  }
}

resource "aws_ses_configuration_set" "failure_rendering_notification" {
  name = "${var.tenant}_failure_rendering_notification_configuration_set"
}

resource "aws_ses_event_destination" "failure_rendering_notification" {
  name                   = "notification_sender_rendering_failure"
  configuration_set_name = aws_ses_configuration_set.failure_rendering_notification.name
  enabled                = true
  matching_types         = ["renderingFailure"]

  //FIXME: Terraform do not support email subscription yet. https://www.terraform.io/docs/providers/aws/r/sns_topic_subscription.html
  sns_destination {
    topic_arn = var.sns_failure_rendering_arn
  }
}

resource "aws_ses_receipt_rule" "error_rendering_to_s3" {
  count         = var.rendering_errors_to_s3_bucket_enabled ? 1 : 0
  name          = "error-rendering-to-s3"
  rule_set_name = aws_ses_receipt_rule_set.default.rule_set_name
  recipients    = ["errors@${element(var.email_domain,0)}"]
  enabled       = true
  tls_policy    = "Require"
  scan_enabled  = var.scan_enabled

  s3_action {
    bucket_name = var.rendering_bucket_name
    position    = 1
  }

  stop_action {
    position = 2
    scope    = "RuleSet"
  }

  depends_on = [module.error_rendering_to_s3]
}

resource "aws_ses_email_identity" "notification_email_sender" {
  email = var.notification_email_sender
}

resource "aws_ses_domain_mail_from" "friday" {
  domain           = var.notification_email_sender
  mail_from_domain = "outbound.ses.${element(var.email_domain,0)}"
}