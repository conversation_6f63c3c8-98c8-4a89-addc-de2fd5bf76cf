module "global" { source = "../global" }

locals {
  tags = {
    Environment = var.environment
    ManagedBy   = "Terraform"
    Component   = module.global.component_tf_state
  }
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0"
    }
  }
}

resource "aws_s3_bucket" "terraform_state" {
  bucket = coalesce(var.backend_bucket_name, "tf-${var.tenant}-backend-infrastructure-${var.environment}")

  lifecycle { prevent_destroy = true }

  tags = local.tags
}

resource "aws_s3_bucket_versioning" "terraform_state_versioning" {
  bucket = aws_s3_bucket.terraform_state.id

  versioning_configuration { status = "Enabled" }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "terraform_state_sse" {
  bucket = aws_s3_bucket.terraform_state.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "AES256"
    }
  }
}

resource "aws_dynamodb_table" "lock_table" {
  name         = var.dynamodb_table_name
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "LockID"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "LockID"
    type = "S"
  }

  tags = local.tags

  lifecycle { prevent_destroy = true }
}
