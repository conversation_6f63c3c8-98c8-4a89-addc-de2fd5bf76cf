variable "backend_bucket_name" {
  description = "Name to be used for the S3 bucket to store state in the backend"
  default = null
}

variable "dynamodb_table_name" {
  description = "Name to be used on the DynamoDB table to lock access to the terraform state backend"
}

variable "environment" {
  description = "The environment"
}

variable "aws_region" {
  description = "The AWS region to use"
}

variable "tenant" {
  description = "The tenant name"
}