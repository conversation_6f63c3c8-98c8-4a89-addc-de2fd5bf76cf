data "aws_iam_policy_document" "ecs_task_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "ecs_task_role" {
  name               = var.ecs_task_role_name
  assume_role_policy = jsonencode(jsondecode(data.aws_iam_policy_document.ecs_task_role.json))
}


data "aws_iam_policy_document" "dynamo-policy-doc" {
  statement {
    sid       = "AllowDynamoPermissions"
    effect    = "Allow"
    resources = var.ecs_dynamo_policy_resource
    actions   = ["dynamodb:*"]
  }
}

resource "aws_iam_policy" "dynamo-policy" {
  count  = var.dynamo_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.dynamo-policy-doc.json

  description = "Allow dynamo permissions"
}

resource "aws_iam_role_policy_attachment" "ecs_task_role_policy_attach_dynamo" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.dynamo_access_enabled ? 1 : 0
  role       = var.ecs_task_role_name
  policy_arn = aws_iam_policy.dynamo-policy[0].arn
}

data "aws_iam_policy_document" "aws_lambda" {
  statement {
    sid       = "AllowLambdaPermissions"

    actions = [
      "lambda:InvokeAsync",
      "lambda:InvokeFunction"
    ]
    resources = var.ecs_lambda_policy_resource
  }
}

resource "aws_iam_policy" "aws_lambda" {
  count  = var.lambda_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.aws_lambda.json

  description = "Allow lambda permissions"
}

resource "aws_iam_role_policy_attachment" "aws_lambda" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.lambda_access_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.aws_lambda[0].arn
}

data "aws_iam_policy_document" "aws_sns" {
  statement {
    sid       = "AllowSNSPermissions"

    actions = [
      "sns:CreateTopic",
      "sns:Publish",
      "sns:Subscribe",
      "sns:ListSubscriptionsByTopic",
      "sns:SetSubscriptionAttributes"
    ]
    resources = var.ecs_sns_policy_resource
  }
  statement {
    sid = "AllowAllSNSPermissions"
    actions = [
      "sns:Publish"
    ]
    not_resources = ["arn:aws:sns:*:*:*"]
  }
  statement {
    sid = "AllowSNSEndpointPermissions"
    actions = [
      "sns:CreatePlatformEndpoint",
      "sns:GetEndpointAttributes"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "aws_sns" {
  count  = var.sns_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.aws_sns.json

  description = "Allow sns permissions"
}

resource "aws_iam_role_policy_attachment" "aws_sns" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.sns_access_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.aws_sns[0].arn
}

data "aws_iam_policy_document" "aws_sqs" {
  statement {
    sid       = "AllowSQSPermissions"

    actions = [
      "sqs:*"
    ]
    resources = var.ecs_sqs_policy_resource
  }

  statement {
    sid = "AllowSQSListQueues"
    actions = [
      "sqs:ListQueues"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "aws_sqs" {
  count  = var.sqs_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.aws_sqs.json

  description = "Allow sqs permissions"
}

resource "aws_iam_role_policy_attachment" "aws_sqs" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.sqs_access_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.aws_sqs[0].arn
}

data "aws_iam_policy_document" "s3" {
  statement {
    sid = "ResourceBasedPolicy"
    effect = "Allow"
    actions = [
      "s3:GetBucketLocation",
      "s3:List*",
    ]
    resources = var.s3_bucket_arns
  }

  statement {
    sid = "WildcardBasedPolicy"
    effect = "Allow"
    actions = [
      "s3:*Object",
      "s3:*ObjectTagging",
    ]
    resources = formatlist("%s/*", var.s3_bucket_arns)
  }
}

resource "aws_iam_policy" "get_object" {
  count  = var.s3_read_objects ? 1 : 0
  policy = data.aws_iam_policy_document.s3.json

  description = "Allow read objects from s3"
}

resource "aws_iam_role_policy_attachment" "get_object" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.s3_read_objects ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.get_object[0].arn
}

data "aws_iam_policy_document" "textract" {
  statement {
    effect = "Allow"
    actions = [
      "textract:DetectDocumentText",
      "textract:AnalyzeDocument"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "textract" {
  count  = var.textract_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.textract.json

  description = "Allow textract permissions"
}

resource "aws_iam_role_policy_attachment" "textract" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.textract_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.textract[0].arn
}

data "aws_iam_policy_document" "send_email" {
  statement {
    effect = "Allow"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail",
      "ses:SendTemplatedEmail"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "send_email" {
  count  = var.send_email ? 1 : 0
  policy = data.aws_iam_policy_document.send_email.json

  description = "Allow send email"
}

resource "aws_iam_role_policy_attachment" "send_email" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.send_email ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.send_email[0].arn
}

data "aws_iam_policy_document" "cognito" {
  count      = var.user_pool_arn_enabled ? 1 : 0
  statement {
    effect = "Allow"
    actions = [
      "cognito-idp:AdminAddUserToGroup",
      "cognito-idp:AdminRemoveUserFromGroup",
      "cognito-idp:AdminCreateUser",
      "cognito-idp:AdminGetUser",
      "cognito-idp:AdminResetUserPassword",
      "cognito-idp:AdminSetUserMFAPreference",
      "cognito-idp:AdminSetUserPassword",
      "cognito-idp:AdminSetUserSettings",
      "cognito-idp:AdminUpdateUserAttributes",
      "cognito-idp:AdminUserGlobalSignOut",
      "cognito-idp:AdminDisableUser",
      "cognito-idp:AdminDeleteUserAttributes"
    ]
    resources = [var.user_pool_arn]
  }
}

resource "aws_iam_policy" "cognito" {
  count      = var.user_pool_arn_enabled ? 1 : 0
  policy     = data.aws_iam_policy_document.cognito[0].json

  description = "Allow cognito permissions"
}

resource "aws_iam_role_policy_attachment" "cognito" {
  count      = var.user_pool_arn_enabled ? 1 : 0
  depends_on = [aws_iam_role.ecs_task_role]
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.cognito[0].arn
}

data "aws_iam_policy_document" "logs" {
  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams",
      "logs:DescribeLogStreams"
    ]
    resources = [
      "arn:aws:logs:${var.aws_region}:*:log-group:/ecs/${var.task_name}:*",
      "arn:aws:logs:${var.aws_region}:*:log-group:/logs/${var.task_name}:*"
    ]
  }
}

resource "aws_iam_policy" "logs" {
  path        = "/"
  description = "Allow publishing to cloudwatch"

  policy = jsonencode(jsondecode(data.aws_iam_policy_document.logs.json))

  #lifecycle { ignore_changes = [policy] }
}

resource "aws_iam_role_policy_attachment" "logs" {
  depends_on = [aws_iam_role.ecs_task_role]
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.logs.arn
}

data "aws_iam_policy_document" "kms" {
  statement {
    actions = [
      "kms:Encrypt",
      "kms:Decrypt"
    ]
    resources = var.kms_key_arns

  }
}

resource "aws_iam_policy" "kms" {
  count      = var.kms_enabled ? 1 : 0
  description = "Allow encrypt and decrypt data"

  policy = data.aws_iam_policy_document.kms.json
}

resource "aws_iam_role_policy_attachment" "kms" {
  count      = var.kms_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.kms[0].arn
}