variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_private_subnet_id" {

}

variable "aws_public_subnet_id" {

}

variable "load_balance_enabled" {
  description = "ALB enabled"
  default = false
}

variable "prefix" {
  description = "Prefix to use on ALB, target group and service names"
}

variable "task_definition" {
  description = "ECS task definition"
}

variable "aws_ecs_cluster" {
  description = "ECS cluster"
}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
  default     = 8080
}

variable "app_protocol" {
  description = "Protocolo entre o ALB e a aplicação"
}

variable "container_name" {
  description = "Name of the container to target external requisitions"
}

variable "app_count" {
  description = "Number of docker containers to run"
  default     = 2
}

variable "health_check_path" {
  default = "/"
}

variable "access_logs_enabled" {
  default = false
  type    = bool
}

variable "access_logs_bucket" {
  default = ""
  type    = string
}

variable "certificate_arn" {
  description = "arn of the certificate to be used on the ALB"
  default = null
  type    = string
}

variable "alternative_certificate_arn_enabled" {
  default = false
}

variable "alternative_certificate_arn" {
  default = ""
  type    = string
}

variable "health_check_healthy_threshold" {
    type    = string
    default = "3"
}
variable "health_check_unhealthy_threshold" {
  type    = string
  default = "6"
}
variable "health_check_interval" {
  type    = string
  default = "60"
}
variable "health_check_matcher" {
  type    = string
  default = "200-499"
}
variable "health_check_timeout" {
  type    = string
  default = "50"
}

variable "use_fargate" {
  default = true
}

variable "certificate_arn_enabled" {
  default = true
  type = bool
}