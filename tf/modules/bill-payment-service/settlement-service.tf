module "ecs_settlement-service" {
  count  = local.has_settlement_service ? 1 : 0
  source = "../settlement-service"
  tenant = var.tenant

  aws_ecs_cluster       = var.aws_ecs_bill_payment_cluster
  environment           = var.environment
  app_environments      = var.app_environments
  aws_private_subnet_id = var.aws_private_subnet_id
  aws_public_subnet_id  = var.aws_public_subnet_id
  aws_vpc_id            = var.aws_vpc_id
  prefix                = var.prefix
  task_definition       = local.settlement_service_task_definition
  certificate_arn       = var.certificate_arn
  dynamo_access_enabled = true
  s3_read_objects       = true
  s3_bucket_arns = [
    var.config_files_bucket_arn,
  ]
  secrets_arns = [
    var.datadog_key_arn,
    aws_secretsmanager_secret.auth-secrets.arn,
    aws_secretsmanager_secret.arbi-app-credentials.arn,
    aws_secretsmanager_secret.celcoin-credentials.arn,
    aws_secretsmanager_secret.celcoin-mtls.arn,
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource
  account_id              = var.account_id
  tags = {
    Project = "settlement-service"
  }
}
