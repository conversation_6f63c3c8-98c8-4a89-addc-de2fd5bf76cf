module "ecs_dda_bills" {
  count = local.has_dda_bills ? 1 : 0

  source                = "../dda-bills-service"
  aws_ecs_cluster       = var.aws_ecs_bill_payment_cluster
  environment           = var.environment
  tenant                = var.tenant
  app_environments      = var.app_environments
  aws_private_subnet_id = var.aws_private_subnet_id
  aws_public_subnet_id  = var.aws_public_subnet_id
  aws_vpc_id            = var.aws_vpc_id
  prefix                = var.prefix
  task_definition       = local.dda_service_task_definition
  certificate_arn       = var.certificate_arn
  dynamo_access_enabled = true
  secrets_arns = [
    var.datadog_key_arn,
    aws_secretsmanager_secret.auth-secrets.arn,
  ]
  sns_topic_arns = [
    module.bill_events_sns.topic_arn,
  ]
  sqs_access_enabled        = true
  sns_access_enabled        = true
  ecs_sqs_policy_resource   = local.ecs_sqs_list_policy_resource
  account_id                = var.account_id
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.bill_payment_table.arn,
    "${aws_dynamodb_table.bill_payment_table.arn}/*",
    aws_dynamodb_table.bill_payment_lock_table.arn,
    "${aws_dynamodb_table.bill_payment_lock_table.arn}/*"
  ]
  tags = {
    Project = "dda-bills-service"
  }

  depends_on = [module.bill_payment_service]
}

resource "aws_cloudwatch_query_definition" "dda_processed_files" {
  count = local.has_dda_bills ? 1 : 0

  name = "DDA/Processed Files"

  log_group_names = [
    module.ecs_dda_bills[0].default_log_group_name
  ]

  query_string = <<EOF
fields @timestamp, message, result, totalItems, filename, level
| filter ispresent(totalItems)
| sort @timestamp desc
| limit 200
EOF
}
