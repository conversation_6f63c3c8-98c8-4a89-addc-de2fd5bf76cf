module "bill_payment_task" {
  source              = "../fargate_task"
  prefix              = "${var.prefix}-bill-payment"
  service_name        = "${var.prefix}-bill-payment-service"
  tenant              = var.tenant
  ecr_repository_name = "bill-payment-api"
  fargate_cpu         = 4096
  fargate_memory      = 8192
  other_container_definitions = {
    cpu    = var.main_task_cpu
    memory = var.main_task_memory
  }
  task_definition       = local.bill_payment_service_task_definition
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.bill_payment_table.arn,
    "${aws_dynamodb_table.bill_payment_table.arn}/*",
    aws_dynamodb_table.bill_payment_event_table.arn,
    "${aws_dynamodb_table.bill_payment_event_table.arn}/*",
    aws_dynamodb_table.bill_payment_lock_table.arn,
    "${aws_dynamodb_table.bill_payment_lock_table.arn}/*",
    aws_dynamodb_table.bill_payment_user_event_table.arn,
    "${aws_dynamodb_table.bill_payment_user_event_table.arn}/*"
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource

  sns_access_enabled = true
  ecs_sns_policy_resource = concat([
    "arn:aws:sns:*:*:${var.tenant}-*", #aqui
  ],
    var.sns_topics)
  s3_read_objects = true
  s3_bucket_arns = [
    # TODO PARAMETERIZE
    module.ses-email-receiver.incoming_emails_bucket_arn,
    module.ses-email-receiver.email_receiver_s3_bucket_arn,
    module.ses-email-receiver.unprocessed_emails_bucket_arn,
    module.ses-email-receiver.quarantine_emails_bucket_arn,
    aws_s3_bucket.bill_payment_documents_bucket.arn,
    aws_s3_bucket.bill_payment_receipts_bucket.arn,
    var.config_files_bucket_arn,
  ]
  textract_enabled = true
  secrets_enabled  = true
  secrets_arns = [
    # TODO PARAMETERIZE, SHOULD BE A MAP
    var.datadog_key_arn, //0
    aws_secretsmanager_secret.auth-secrets.arn, //1
    aws_secretsmanager_secret.arbi-app-credentials.arn, //2
    aws_secretsmanager_secret.arbi-ecm-credentials.arn, //3
    aws_secretsmanager_secret.blip.arn, //4
    aws_secretsmanager_secret.clearsale-credentials.arn, //5
    aws_secretsmanager_secret.keystore.arn, //6
    aws_secretsmanager_secret.cielo_credentials.arn, //7
    aws_secretsmanager_secret.software-express.arn, //8
    aws_secretsmanager_secret.vehicle-debts.arn,
    aws_secretsmanager_secret.whatsapp-secrets.arn,
    aws_secretsmanager_secret.celcoin-mtls.arn,
    #aws_secretsmanager_secret.bigdatacorp_credentials.arn,
    #aws_secretsmanager_secret.friday_userpilot.arn,
    #aws_secretsmanager_secret.via1_celcoin-credentials.arn,
    #aws_secretsmanager_secret.friday_intercom-credentials.arn
  ]
  secrets_map           = local.bill_payment_secrets_mapping
  send_email            = true
  kms_enabled           = false
  cluster_name          = var.aws_ecs_bill_payment_cluster.name
  container_name        = local.main_container_name
  app_environments      = var.app_environments
  environment           = var.environment
  account_id            = var.account_id
  app_port              = 8443
  tags                  = var.tags
  user_pool_arn_enabled = var.user_pool_arn_enabled
  user_pool_arn         = var.user_pool_arn
  ephemeral_storage     = 21
  kms_key_arns          = var.kms_key_arns
}

module "bill_payment_service" {
  source = "../fargate_service"

  aws_ecs_cluster       = var.aws_ecs_bill_payment_cluster
  aws_private_subnet_id = var.aws_private_subnet_id
  aws_public_subnet_id  = var.aws_public_subnet_id
  aws_vpc_id            = var.aws_vpc_id
  prefix                = "${var.prefix}-bill-payment"
  container_name        = local.main_container_name
  app_port              = 8443
  app_count             = 1
  app_protocol          = "HTTPS"
  health_check_path     = "/health"
  task_definition       = module.bill_payment_task
  certificate_arn       = var.certificate_arn
  load_balance_enabled  = true
}