
locals {
  secrets_force_recreation = false
  secrets_recovery_window_in_days = local.secrets_force_recreation ? 0 : 7
}

# resource "aws_secretsmanager_secret" "via1_auth-secrets" {
#   name = "friday/auth-secrets"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "via1_celcoin-credentials" {
#   name = "friday/celcoin-credentials"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "via1_arbi-app-credentials" {
#   name = "friday/arbi-app-credentials"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "via1_arbi-ecm-credentials" {
#   name = "friday/arbi-ecm-credentials"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "via1_clearsale-credentials" {
#   name = "friday/clearsale-credentials"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "via1_keystore" {
#   name = "friday/keystore"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "software_express" {
#   name = "friday/software_express"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "blip_password" {
#   name = "bill-payment-api/blip-password"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "openai_key" {
#   name = "friday/openai_key"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
# resource "aws_secretsmanager_secret" "waba_token" {
#   name = "friday/waba_token"
#   recovery_window_in_days = local.secrets_recovery_window_in_days
# }
#
resource "aws_secretsmanager_secret" "cielo_credentials" {
  name = "friday/cielo-credentials"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}
#
# resource "aws_secretsmanager_secret" "vehicle_debts" {
#   name = "friday/vehicle_debts"
# }
#
# resource "aws_secretsmanager_secret" "celcoin_mtls" {
#   name = "friday/celcoin-mtls"
# }
#
# resource "aws_secretsmanager_secret" "whatsapp_secrets" {
#   name = "friday/whatsapp-secrets"
# }

# Migrar motorola

resource "aws_secretsmanager_secret" "auth-secrets" {
  name = "${var.tenant}/auth-secrets"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "celcoin-credentials" {
  name = "${var.tenant}/celcoin-credentials"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "arbi-app-credentials" {
  name = "${var.tenant}/arbi-app-credentials"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "arbi-ecm-credentials" {
  name = "${var.tenant}/arbi-ecm-credentials"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "clearsale-credentials" {
  name = "${var.tenant}/clearsale-credentials"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "keystore" {
  name = "${var.tenant}/keystore"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "software-express" {
  name = "${var.tenant}/software_express"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "blip" {
  name = "${var.tenant}/blip-password"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "openai" {
  name = "${var.tenant}/openai_key"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "waba" {
  name = "${var.tenant}/waba_token"
  recovery_window_in_days = local.secrets_recovery_window_in_days
}

resource "aws_secretsmanager_secret" "vehicle-debts" {
  name = "${var.tenant}/vehicle_debts"
}

resource "aws_secretsmanager_secret" "celcoin-mtls" {
  name = "${var.tenant}/celcoin-mtls"
}

resource "aws_secretsmanager_secret" "whatsapp-secrets" {
  name = "${var.tenant}/whatsapp-secrets"
}

# FIRST VALUE

resource "aws_secretsmanager_secret_version" "whatsapp_secrets" {
  secret_id = aws_secretsmanager_secret.whatsapp-secrets.id
  secret_string = jsonencode({
    "SALT"       = "default_salt_value"
    "PRIVATE_KEY" = "default_privatekey_value"
  })
}

resource "aws_secretsmanager_secret_version" "celcoin_mtls" {
  secret_id = aws_secretsmanager_secret.celcoin-mtls.id
  secret_string = "FIRST_VALUE"
}

resource "aws_secretsmanager_secret_version" "vehicle_debts" {
  secret_id = aws_secretsmanager_secret.vehicle-debts.id
  secret_string = jsonencode({
    "USERNAME" = local.first_value
    "PASSWORD" = local.first_value
  })
}

resource "aws_secretsmanager_secret_version" "openai_key" {
  secret_id = aws_secretsmanager_secret.openai.id
  secret_string = "FIRST_VALUE"
}

resource "aws_secretsmanager_secret_version" "software_express" {
  secret_id = aws_secretsmanager_secret.software-express.id
  secret_string = jsonencode({
    "MERCHANT_ID" = local.first_value
    "MERCHANT_KEY" = local.first_value
  })
}

resource "aws_secretsmanager_secret_version" "cielo_credentials-first-value" {
  secret_id = aws_secretsmanager_secret.cielo_credentials.id
  secret_string = jsonencode({
    "CIELO_MERCHANT_ID"      = local.first_value
    "CIELO_MERCHANT_KEY"     = local.first_value
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_secretsmanager_secret_version" "via1_auth-secrets-first-value" {
  secret_id = aws_secretsmanager_secret.auth-secrets.id
  secret_string = jsonencode({
    "ARBI_CALLBACK_SECRET"   = local.first_value
    "ARBI_CALLBACK_IDENTITY" = local.first_value
    "ARBI_MTLS_PASSWORD"     = local.first_value
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_secretsmanager_secret_version" "via1_arbi-app-credentials-first-value" {
  secret_id = aws_secretsmanager_secret.arbi-app-credentials.id
  secret_string = jsonencode({
    "CLIENT_SECRET" = local.first_value
    "USER_TOKEN"    = local.first_value
    "CLIENT_ID"     = local.first_value
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_secretsmanager_secret_version" "via1_keystore-first-value" {
  secret_id = aws_secretsmanager_secret.keystore.id
  secret_string = jsonencode({
    "MASTERKEY"   = local.first_value
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_secretsmanager_secret_version" "via1_clearsale-credentials-first-value" {
  secret_id = aws_secretsmanager_secret.clearsale-credentials.id
  secret_string = jsonencode({
    "HOST"        = local.first_value
    "USERNAME"    = local.first_value
    "PASSWORD"    = local.first_value
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_secretsmanager_secret_version" "via1_arbi-ecm-credentials-first-value" {
  secret_id = aws_secretsmanager_secret.arbi-ecm-credentials.id
  secret_string = jsonencode({
    "CLIENT"   = local.first_value
    "API_KEY"  = local.first_value
    "USERNAME" = local.first_value
    "PASSWORD" = local.first_value
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

resource "aws_secretsmanager_secret_version" "blip-password-first-value" {
  secret_id = aws_secretsmanager_secret.blip.id
  secret_string = local.first_value

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

# UNUSED

/*resource "aws_secretsmanager_secret" "friday_intercom-credentials" {
  name = "friday/intercom-credentials"
}
*/

/*resource "aws_secretsmanager_secret" "friday_userpilot" {
  name = "friday/userpilot"
}*/

/*resource "aws_secretsmanager_secret" "bigdatacorp_credentials" {
  name = "friday/bigdatacorp-credentials"
}*/