module "ai_chatbot" {
  source = "../../modules/ai-chatbot"

  count = var.chatbot_enabled ? 1: 0

  aws_ecs_bill_payment_cluster = var.aws_ecs_bill_payment_cluster
  account_id                   = var.account_id
  tenant                       = var.tenant
  aws_region                   = "us-east-1"
  environment                  = var.environment
  app_environments = length(var.chatbot_adicional_tenants) > 0 ? "${local.app_environments_without_friday},${join(",",var.chatbot_adicional_tenants)}" : local.app_environments_without_friday
  task_definition              = local.chatbot_task_definition
  private_subnets              = var.aws_private_subnet_id
  public_subnets               = var.aws_public_subnet_id
  vpc_id                       = var.aws_vpc_id
  certificate_arn              = var.certificate_arn
  ecs_sqs_list_policy_resource = local.ecs_sqs_list_policy_resource
  shedlock_table_arn           = aws_dynamodb_table.bill_payment_lock_table.arn
  billpayment_table_arn        = aws_dynamodb_table.bill_payment_table.arn
  blip_password_secret_arn     = aws_secretsmanager_secret.blip.arn
  open_ai_key_secret_arn       = aws_secretsmanager_secret.openai.arn
  waba_token_arn               = aws_secretsmanager_secret.waba.arn
  datadog_secret_arn           = var.datadog_key_arn
  tags                         = var.tags
  chatbot_adicional_secrets_map = var.chatbot_adicional_secrets_map
}