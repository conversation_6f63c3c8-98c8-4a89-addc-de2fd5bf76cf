resource "aws_route53_zone" "private_zone" {
  name = "friday.internal"
  vpc {
    vpc_id = var.aws_vpc_id
  }
}

resource "aws_route53_zone" "web_domain" {
  name = var.web_domain
  vpc {
    vpc_id = var.aws_vpc_id
  }
}

resource "aws_route53_record" "elasticache_internal_dns" {
  zone_id = aws_route53_zone.private_zone.id
  name    = "${var.tenant}-bill-payment-cache"
  type    = "CNAME"
  ttl     = 300
  records = [
    aws_elasticache_cluster.bill-payment-cache-cluster.cache_nodes.0.address
  ]

  depends_on = [aws_elasticache_cluster.bill-payment-cache-cluster]
}

resource "aws_route53_record" "api_internal" {
  zone_id = aws_route53_zone.web_domain.id
  name    = "api-internal"
  type    = "CNAME"
  ttl     = 300
  records = [
    module.bill_payment_service.alb_hostname
  ]

  depends_on = [module.bill_payment_service.alb_hostname]
}

resource "aws_route53_record" "settlement_host" {
  count = local.has_settlement_service ? 1 : 0
  zone_id = aws_route53_zone.web_domain.id
  name    = "liquidacao"
  type    = "CNAME"
  ttl     = 300
  records = [
    module.ecs_settlement-service[0].alb_hostname
  ]

  depends_on = [module.ecs_settlement-service]
}

output "private_zone_name" {
  value = aws_route53_zone.private_zone.name
}