locals {
  jazz_event_bus_payments_sqs_name     = "jazz_event_bus_payments"
  jazz_onboarding_sqs_name             = "jazz_onboarding"
}

// JAZZ EVENT BUS

module "jazz_event_bus_payments_queue" {
  count = var.jazz_enabled ? 1 : 0
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.jazz_event_bus_payments_policy[0].json
  name                       = local.jazz_event_bus_payments_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 600
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.jazz_event_bus_payments_dlq[0].this_sqs_queue_arn}\",\"maxReceiveCount\":3}"

  depends_on = [module.jazz_event_bus_payments_dlq]
}

module "jazz_event_bus_payments_dlq" {
  count = var.jazz_enabled ? 1 : 0
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${local.jazz_event_bus_payments_sqs_name}_dlq"
}

resource "aws_sns_topic_subscription" "event_bus_jazz_payments_subscription" {
  count = var.jazz_enabled ? 1 : 0
  topic_arn            = module.event_bus_sns.topic_arn
  protocol             = "sqs"
  endpoint             = module.jazz_event_bus_payments_queue[0].this_sqs_queue_arn
  filter_policy        = jsonencode({ "eventType" = tolist(["PAYMENT"]) })
  raw_message_delivery = true

  depends_on = [module.event_bus_sns]
}

data "aws_iam_policy_document" "jazz_event_bus_payments_policy" {
  count = var.jazz_enabled ? 1 : 0
  statement {
    sid       = "Stmt1740144494590"
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.jazz_event_bus_payments_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = [module.event_bus_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
  statement {
    sid = "Stmt1740144616292"
    actions   = ["sqs:ReceiveMessage", "sqs:DeleteMessage"]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.jazz_event_bus_payments_sqs_name}"
    ]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/iam-role-nifi-frd-api"]
    }
    effect = "Allow"
  }
  statement {
    sid = "Stmt1740144947934"
    actions   = ["sqs:ReceiveMessage", "sqs:DeleteMessage"]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.jazz_event_bus_payments_sqs_name}"
    ]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/iam-role-nifi-frd-api"]
    }
    effect = "Allow"
  }
}

// JAZZ ONBOARDING

module "jazz_onboarding_queue" {
  count = var.jazz_enabled ? 1 : 0
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.jazz_onboarding_queue_policy[0].json
  name                       = local.jazz_onboarding_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 600
  message_retention_seconds  = 1209600
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.jazz_onboarding_dlq[0].this_sqs_queue_arn}\",\"maxReceiveCount\":5}"
}

module "jazz_onboarding_dlq" {
  count = var.jazz_enabled ? 1 : 0
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${local.jazz_onboarding_sqs_name}_dlq"
  message_retention_seconds = 1209600
  visibility_timeout_seconds = 600
}

data "aws_iam_policy_document" "jazz_onboarding_queue_policy" {
  count = var.jazz_enabled ? 1 : 0
  statement {
    sid = "JazzSendMessage"
    actions   = ["sqs:SendMessage"]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.jazz_onboarding_sqs_name}"
    ]
    principals {
      type        = "AWS"
      identifiers = [
        "arn:aws:iam::************:role/iam-role-nifi-frd-api",
        "arn:aws:iam::************:role/iam-role-nifi-frd-api"
      ]
    }
    effect = "Allow"
  }
}
