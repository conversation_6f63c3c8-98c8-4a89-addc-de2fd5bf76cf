resource "aws_dynamodb_table" "bill_payment_table" {
  name         = "${var.tenant}-BillPayment"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

    point_in_time_recovery {
        enabled = true
    }

  ttl {
    attribute_name = "ExpirationTTL"
    enabled        = true
  }

  deletion_protection_enabled = true

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex3"
    hash_key        = "GSIndex3PrimaryKey"
    range_key       = "GSIndex3ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex4"
    hash_key        = "GSIndex4PrimaryKey"
    range_key       = "GSIndex4ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }
}

resource "aws_dynamodb_table" "bill_payment_event_table" {
  name         = "${var.tenant}-BillEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  point_in_time_recovery {
    enabled = true
  }

  deletion_protection_enabled = true

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "N"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }
}

resource "aws_dynamodb_table" "bill_payment_lock_table" {
  name         = "${var.tenant}-Shedlock"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "_id"

  attribute {
    name = "_id"
    type = "S"
  }

  deletion_protection_enabled = true

}

resource "aws_dynamodb_table" "bill_payment_user_event_table" {
  name         = "${var.tenant}-UserEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  point_in_time_recovery {
    enabled = true
  }

  deletion_protection_enabled = true

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
  }
}


locals {
  dynamodb_vault_name = "${var.tenant}-dynamodb-backup-vault"
}

resource "aws_backup_vault" "bill_payment_dynamodb_backup_vault" {
  name = local.dynamodb_vault_name
  # Outras configurações do vault
}

resource "aws_backup_plan" "bill_payment_dynamodb_backup_plan" {
  depends_on = [aws_backup_vault.bill_payment_dynamodb_backup_vault]

  name  = "${var.tenant}-dynamodb-backup-plan"
  count = var.aws_dynamodb_backup_enabled ? 1 : 0

  rule {
    rule_name         = "DynamoDBHourlySnapshotsRule"
    target_vault_name = local.dynamodb_vault_name
    schedule          = "cron(0 6/1 ? * * *)"
    start_window      = 60
    completion_window = 120

    lifecycle {
      delete_after = 3  # Retain backups for 3 days
    }
  }

  rule {
    rule_name         = "DynamoDBDailyBackupRule"
    target_vault_name = local.dynamodb_vault_name
    schedule          = "cron(10 6 ? * * *)"
    start_window      = 480
    completion_window = 1008

    lifecycle {
      delete_after = 8  # Retain backups for 35 days
    }
  }

  rule {
    rule_name         = "DynamoDBWeeklyBackupRule"
    target_vault_name = local.dynamodb_vault_name
    schedule          = "cron(20 6 ? * 7 *)"
    start_window      = 480
    completion_window = 10080

    lifecycle {
      delete_after = 35  # Retain backups for 35 days
    }
  }

  rule {
    rule_name         = "DynamoDBMonthlyBackupRule"
    target_vault_name = local.dynamodb_vault_name
    schedule          = "cron(30 6 1 * ? *)"
    start_window      = 480
    completion_window = 1440

    lifecycle {
      delete_after = 390  # Retain backups for 390 days
      cold_storage_after = 60
    }
  }

  rule {
    rule_name         = "DynamoDBYearlyBackupRule"
    target_vault_name = local.dynamodb_vault_name
    schedule          = "cron(40 6 2 1 ? *)"
    start_window      = 480
    completion_window = 2880

    lifecycle {
      delete_after = 2190  # Retain backups for 2190 days
      cold_storage_after = 60
    }
  }
}

resource "aws_backup_selection" "bill_payment_dynamodb_backup_selection" {
  name         = "${var.tenant}-dybanodb-backup-selection"
  count        = var.environment == "production" ? 1 : 0
  iam_role_arn = aws_iam_role.bill_payment_dynamodb_backup[0].arn
  plan_id      = aws_backup_plan.bill_payment_dynamodb_backup_plan[0].id

  resources = [
    aws_dynamodb_table.bill_payment_table.arn,
    aws_dynamodb_table.bill_payment_event_table.arn
  ]

  depends_on = [aws_backup_plan.bill_payment_dynamodb_backup_plan]
}

resource "aws_iam_role" "bill_payment_dynamodb_backup" {
  name  = "${var.tenant}-dynamodb-backup"
  count = var.environment == "production" ? 1 : 0

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "backup.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "bill_payment_dynamodb_backup" {
  count      = var.environment == "production" ? 1 : 0
  role       = aws_iam_role.bill_payment_dynamodb_backup[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"
  depends_on = [aws_iam_role.bill_payment_dynamodb_backup[0]]
}

resource "aws_elasticache_subnet_group" "bill-payment-cache-cluster" {
  name       = "${var.prefix}-bill-payment-cache-subnet"
  subnet_ids = var.aws_private_subnet_id
}

resource "aws_elasticache_cluster" "bill-payment-cache-cluster" {
  cluster_id           = "${var.prefix}-bill-payment-cache"
  engine               = "redis"
  node_type            = "cache.t2.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  engine_version       = "7.1"
  subnet_group_name    = aws_elasticache_subnet_group.bill-payment-cache-cluster.name
  security_group_ids = [aws_security_group.bill-payment-cache-cluster.id]
}
