locals {
  account_id                           = var.account_id
  has_dda_bills                        = false
  has_settlement_service               = false
  main_container_name                  = "BillPaymentAPI"
  bill_events_sns_name                 = "${var.tenant}-bill-events-${var.environment}"
  event_bus_sns_name                   = "${var.tenant}-event-bus-${var.environment}"
  bill_payment_service_task_definition = "${var.task_definitions_base_path}/bill-payment.json"
  settlement_service_task_definition   = "${var.task_definitions_base_path}/settlement-service.json"
  dda_service_task_definition          = "${var.task_definitions_base_path}/dda-service.json"
  chatbot_task_definition              = "${var.task_definitions_base_path}/ai-chatbot.json"
  first_value                          = "FIRST_VALUE"

  ecs_sqs_list_policy_resource = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:${var.tenant}-*"]
  # ler s3 ver se é por bucket mesmo.
  # ler sns
  # ver logs do cloudwatch. Talvez o nome esteja cagado

  # App environments without friday for chatbot
  app_environments_without_friday = replace(var.app_environments, ",friday", "")

  bill_payment_secrets_mapping = {
    "INTEGRATIONS_CIELO_CREDENTIALS_MERCHANT_ID"             = "${aws_secretsmanager_secret.cielo_credentials.arn}:CIELO_MERCHANT_ID::"
    "INTEGRATIONS_CIELO_CREDENTIALS_MERCHANT_KEY"            = "${aws_secretsmanager_secret.cielo_credentials.arn}:CIELO_MERCHANT_KEY::"
    "INTEGRATIONS_VEHICLE_DEBTS_USERNAME"                    = "${aws_secretsmanager_secret.vehicle-debts.arn}:USERNAME::"
    "INTEGRATIONS_VEHICLE_DEBTS_PASSWORD"                    = "${aws_secretsmanager_secret.vehicle-debts.arn}:PASSWORD::"
    "WHATSAPPFLOW_SALT"                                      = "${aws_secretsmanager_secret.whatsapp-secrets.arn}:SALT::"
    "WHATSAPPFLOW_PRIVATE_KEY"                               = "${aws_secretsmanager_secret.whatsapp-secrets.arn}:PRIVATE_KEY::"
    "MICRONAUT_HTTP_SERVICES_CELCOIN_SSL_KEY_STORE_PASSWORD" = "${aws_secretsmanager_secret.celcoin-mtls.arn}:MTLS_P12_PASSWORD::"
  }
}