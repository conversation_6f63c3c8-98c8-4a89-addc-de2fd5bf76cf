resource "aws_s3_bucket" "bill_payment_documents_bucket" {
  bucket = "${var.account_id}-${var.tenant}-user-documents"

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    #prevent_destroy = true
  }

  versioning {
    enabled = true
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}

resource "aws_s3_bucket" "bill_payment_receipts_bucket" {
  bucket = "${var.account_id}-${var.tenant}-bill-receipts"

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    #prevent_destroy = true
  }

  versioning {
    enabled = true
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}