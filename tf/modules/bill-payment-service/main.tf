resource "aws_security_group" "bill-payment-cache-cluster" {
  name        = "${var.prefix}-bill-payment-cache-cluster-sg"
  description = "${var.prefix}-bill-payment-cache-cluster"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "6"
    from_port       = "6379"
    to_port         = "6379"
    security_groups = compact(
      [
        length(module.ecs_dda_bills) > 0 ? module.ecs_dda_bills[0].ecs_tasks_security_group_id : null,
        module.bill_payment_service.ecs_tasks_security_group_id,
      ]
    )
  }
}

module "ses-email-receiver" {
  source                                                 = "../ses"
  tenant                                                 = var.tenant
  ses_receiver_rule_name                                 = var.ses_receiver_rule_name
  email_domain                                           = var.email_domain
  scan_enabled                                           = true
  ses_bucket_name                                        = var.ses_bucket_name

  ses_unprocessed_emails_bucket_name                     = var.ses_unprocessed_emails_bucket_name
  ses_unprocessed_emails_bucket_replication_enabled      = var.ses_unprocessed_emails_bucket_replication_enabled
  ses_unprocessed_emails_bucket_replication_rule_id      = var.ses_unprocessed_emails_bucket_replication_rule_id
  ses_unprocessed_emails_bucket_replication_destination  = var.ses_unprocessed_emails_bucket_replication_destination
  ses_unprocessed_emails_bucket_replication_role         = var.ses_unprocessed_emails_bucket_replication_role
  region                                                 = var.aws_region

  ses_incoming_emails_bucket_name                        = var.ses_incoming_emails_bucket_name
  ses_incoming_emails_bucket_replication_enabled         = var.ses_incoming_emails_bucket_replication_enabled
  ses_incoming_emails_bucket_replication_rule_id         = var.ses_incoming_emails_bucket_replication_rule_id
  ses_incoming_emails_bucket_replication_destination     = var.ses_incoming_emails_bucket_replication_destination
  ses_incoming_emails_bucket_replication_role            = var.ses_incoming_emails_bucket_replication_role

  rendering_errors_to_s3_bucket_enabled                  = var.rendering_errors_to_s3_bucket_enabled
  rendering_bucket_name                                  = "${var.account_id}-${var.tenant}-email-template-rendering-errors-contas"
  sns_receiver_emails_arn                                = module.incoming_emails.topic_arn
  sns_failure_rendering_arn                              = module.failure_rendering_notification.topic_arn
  quarantine_emails_bucket_name                          = var.quarantine_emails_bucket_name
  notification_email_sender                              = var.notification_email_sender
}