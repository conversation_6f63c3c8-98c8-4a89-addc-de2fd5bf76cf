variable "environment" {
  type = string
}

variable "account_id" {
  type = string
}

variable "aws_region" {
  type = string
}

variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_dynamodb_backup_enabled" {
  type = bool
  default = true
}

variable "aws_private_subnet_id" {}

variable "aws_public_subnet_id" {}

variable "aws_ecs_bill_payment_cluster" {}

variable "prefix"{
  type = string
}

variable "certificate_arn" {}

variable "tags" {
  description = "Tags to use on project components"
  type = map
}

variable "datadog_key_arn"{
  type = string
}

variable "config_files_bucket_arn" {
  type = string
}

variable "app_environments"{
  type = string
}

variable "sns_topics" {
  type = list(string)
  default = []
}

variable "incoming_emails_sns_name" {
  type = string
}

variable "incoming_emails_sqs_name" {
  type = string
}

variable "web_domain" {
  type = string
}

variable "ses_receiver_rule_name" {
  type = string
}

variable "email_domain" {
  type = list(string)
}

variable "ses_bucket_name" {
  type = string
}

variable "ses_unprocessed_emails_bucket_name" {
  type = string
}

variable "rendering_errors_to_s3_bucket_enabled" {
  type = bool
}

variable "quarantine_emails_bucket_name" {
  type = string
}

variable "notification_email_sender" {
  type = string 
}

variable "ses_unprocessed_emails_bucket_replication_enabled" {
  type    = bool
}

variable "ses_unprocessed_emails_bucket_replication_rule_id" {
  type    = string
}

variable "ses_unprocessed_emails_bucket_replication_destination" {
  type    = string
}

variable "ses_unprocessed_emails_bucket_replication_role" {
  type    = string
}

variable "ses_incoming_emails_bucket_name" {
  type    = string
}

variable "ses_incoming_emails_bucket_replication_enabled" {
  type    = bool
}

variable "ses_incoming_emails_bucket_replication_rule_id" {
  type    = string
}

variable "ses_incoming_emails_bucket_replication_destination" {
  type    = string
}

variable "ses_incoming_emails_bucket_replication_role" {
  type    = string
}

variable "task_definitions_base_path" {
    type    = string
}

variable "user_pool_arn_enabled" {
  type = bool
}

variable "user_pool_arn" {
  type = string
}

variable "kms_key_arns" {
  type = list(string)
}

variable "main_task_cpu" {
  type = number
  default = 4045
}

variable "main_task_memory" {
  type = number
  default = 7936
}

variable "jazz_enabled" {
  type = bool
}

variable "chatbot_adicional_secrets_map" {
  type = map(string)
}

variable "chatbot_adicional_tenants" {
  type = list(string)
}

variable "tenant" {
  type = string
}

variable "chatbot_enabled" {
  type = bool
}