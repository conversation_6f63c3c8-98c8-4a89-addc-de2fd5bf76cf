module "bill_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.bill_events_sns_name
}

module "event_bus_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.event_bus_sns_name
}

module "incoming_emails" {
  source = "terraform-aws-modules/sns/aws"
  name   = var.incoming_emails_sns_name
}

module "failure_rendering_notification" {
  source = "terraform-aws-modules/sns/aws"
  name   = "${var.tenant}-rendering-failed-emails"
}