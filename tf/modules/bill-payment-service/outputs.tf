output "user_table_arn" {
  value = aws_dynamodb_table.bill_payment_table.arn
}

output "bill_payment_api_alb_hostname" {
  value = module.bill_payment_service.alb_hostname
}

output "bill_payment_api_alb_alb_zone_id" {
  value = module.bill_payment_service.alb_zone_id
}

output "bill_payment_elasticache_url" {
  value = aws_elasticache_cluster.bill-payment-cache-cluster.cache_nodes.0.address
}

output "incoming_emails_s3_arn" {
  value = module.ses-email-receiver.incoming_emails_bucket_arn
}

output "alb_arn" {
  value = module.bill_payment_service.alb_arn
}

output "notification_email_sender_arn" {
  value = module.ses-email-receiver.notification_email_sender_arn
}