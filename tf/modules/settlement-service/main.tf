locals {
  service_name        = "${var.prefix}-settlement"
  container_name      = "settlement-serviceAPI"
  ecr_repository_name = "settlement-service-api"
  app_port            = 8443
}

module "fargate_service" {
  source                = "../fargate_service"
  aws_ecs_cluster       = var.aws_ecs_cluster
  aws_private_subnet_id = var.aws_private_subnet_id
  aws_public_subnet_id  = var.aws_public_subnet_id
  aws_vpc_id            = var.aws_vpc_id
  prefix                = local.service_name
  container_name        = local.container_name
  app_port              = local.app_port
  app_count             = 0
  app_protocol          = "HTTPS"
  health_check_path     = "/health"
  task_definition       = module.fargate_task
  certificate_arn       = var.certificate_arn
  load_balance_enabled  = true
}

module "fargate_task" {
  source       = "../fargate_task"
  prefix       = local.service_name
  service_name = "${local.service_name}-service"
  tenant       = var.tenant

  ecr_repository_name = local.ecr_repository_name
  fargate_cpu         = 512
  fargate_memory      = 2048
  other_container_definitions = {
    cpu    = var.environment == "production" ? 502 : 502
    memory = var.environment == "production" ? 1792 : 1792
  }
  task_definition       = var.task_definition
  dynamo_access_enabled = var.dynamo_access_enabled
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.service_main_table.arn,
    "${aws_dynamodb_table.service_main_table.arn}/*",
    aws_dynamodb_table.service_event_table.arn,
    "${aws_dynamodb_table.service_event_table.arn}/*",
    aws_dynamodb_table.service_lock_table.arn,
    "${aws_dynamodb_table.service_lock_table.arn}/*",
  ]
  sqs_access_enabled      = var.sqs_access_enabled
  ecs_sqs_policy_resource = var.ecs_sqs_policy_resource

  sns_access_enabled = true
  ecs_sns_policy_resource = [
    module.settlement_events_sns.topic_arn
  ]
  s3_read_objects = var.s3_read_objects
  s3_bucket_arns  = var.s3_bucket_arns
  secrets_enabled = true
  secrets_arns = concat(
    var.secrets_arns, [
      aws_secretsmanager_secret.client_credentials.arn
    ]
  )
  cluster_name     = var.aws_ecs_cluster.name
  container_name   = local.container_name
  app_environments = var.app_environments
  environment      = var.environment
  account_id       = var.account_id
  tags             = var.tags
  app_port         = local.app_port
}

module "settlement_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = "settlement-events-${var.environment}"
}

module "settlement_service_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "settlement-service-dlq"
}

module "settlement_response" {
  source         = "terraform-aws-modules/sqs/aws"
  version        = "~> 2.0"
  name           = "settlement-response"
  redrive_policy = "{\"deadLetterTargetArn\":\"${module.settlement_service_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":10}"

  tags = {
    Environment = var.environment
  }
}

resource "aws_secretsmanager_secret" "client_credentials" {
  name = "settlement-service/client-credentials"
}
