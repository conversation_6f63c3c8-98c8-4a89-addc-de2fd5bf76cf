module "settlement-request-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-request-alarm"
  alarm_description   = "Settlement - Request Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-request"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}

module "settlement-query-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-query-alarm"
  alarm_description   = "Settlement - Query Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-query"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-response-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-response-alarm"
  alarm_description   = "Settlement - Response Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-response"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-processed-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-processed-alarm"
  alarm_description   = "Settlement - Processed Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 300
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-processed"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-requested-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-requested-alarm"
  alarm_description   = "Settlement - Requested Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 3600
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-requested"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}

module "settlement-validation-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-validation-alarm"
  alarm_description   = "Settlement - Validation Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 300
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-validation"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}
