resource "aws_dynamodb_table" "service_main_table" {
  name         = "SettlementService-Main"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "SortKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "SortKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1SortKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1SortKey"
    projection_type = "ALL"
  }

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

resource "aws_dynamodb_table" "service_event_table" {
  name         = "SettlementService-Event"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "SortKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "SortKey"
    type = "N"
  }

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

resource "aws_dynamodb_table" "service_lock_table" {
  name         = "SettlementService-Lock"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "_id"

  attribute {
    name = "_id"
    type = "S"
  }

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
