locals {
  secret_json = jsondecode(data.aws_secretsmanager_secret_version.datadog_key_version.secret_string)
  datadog_api_key = local.secret_json["DD_API_KEY"]
  lambda_hash = filebase64sha256("${path.module}/lambda/token_sender/index.js")
}

// Tópico SNS para tokens do Cognito
resource "aws_sns_topic" "cognito_token" {
  name = "${var.user_pool_name}-cognito-token"

  tags = {
    Environment = var.environment
  }
}

data "aws_secretsmanager_secret" "datadog_key" {
  arn = var.datadog_key_arn
}

data "aws_secretsmanager_secret_version" "datadog_key_version" {
  secret_id = data.aws_secretsmanager_secret.datadog_key.id
}

// Lambda function para custom sender do Cognito
module "token_sender" {
  source  = "DataDog/lambda-datadog/aws"
  version = "3.0.0"

  filename      = data.archive_file.lambda_zip.output_path
  function_name = "${var.tenant_name}-cognito-token-sender"
  role          = aws_iam_role.lambda_token_role.arn
  handler       = "index.handler"
  runtime       = "nodejs20.x"
  timeout       = 30
  memory_size   = 128

  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  datadog_extension_layer_version = 74
  datadog_node_layer_version      = 123

  environment_variables = {
    "USE_HTTP_DELIVERY": true
    "TENANT_ID" : upper(var.tenant_name)
    "TOPIC_ARN" : aws_sns_topic.cognito_token.arn
    "DATADOG_API_KEY" : local.datadog_api_key
    "DD_ENV" : var.environment
    "DD_SERVICE" : "${var.tenant_name}-cognito-token-sender"
    "DD_SITE" : "datadoghq.com"
    "DD_VERSION" : "1.0.0"
    # Minimize cold start duration (https://docs.datadoghq.com/serverless/aws_lambda/installation/nodejs/?tab=terraform#minimize-cold-start-duration)
    "DD_TRACE_OTEL_ENABLED" : "false"
    "DD_PROFILING_ENABLED" : "false"
    "DD_SERVERLESS_APPSEC_ENABLED" : "false"
  }
}

// Arquivo ZIP para a Lambda
data "archive_file" "lambda_zip" {
  type        = "zip"
  output_path = "${path.module}/lambda/token_sender_${local.lambda_hash}.zip"
  source_file = "${path.module}/lambda/token_sender/"
}

// IAM role para a Lambda
resource "aws_iam_role" "lambda_token_role" {
  name = "${var.user_pool_name}-token-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

// Política para a Lambda acessar CloudWatch Logs e SNS
resource "aws_iam_role_policy" "lambda_sns_policy" {
  name = "${var.user_pool_name}-lambda-sns-policy"
  role = aws_iam_role.lambda_token_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = [
          aws_sns_topic.cognito_token.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = [
          aws_kms_key.cognito_custom_sender.arn
        ]
      }
    ]
  })
}

// Permissão para o Cognito invocar a Lambda
resource "aws_lambda_permission" "cognito_token" {
  statement_id  = "AllowCognitoInvoke"
  action        = "lambda:InvokeFunction"
  function_name = module.token_sender.function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.user_pool.arn
}

// KMS key para criptografia do custom sender
resource "aws_kms_key" "cognito_custom_sender" {
  description             = "KMS key for Cognito custom sender"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = {
    Environment = var.environment
  }
}

resource "aws_kms_alias" "cognito_custom_sender" {
  name          = "alias/${var.user_pool_name}-custom-sender"
  target_key_id = aws_kms_key.cognito_custom_sender.key_id
}
