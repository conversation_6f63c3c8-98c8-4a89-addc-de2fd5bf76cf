resource "aws_cognito_user_pool_domain" "main" {
  domain          = var.user_pool_domain_name
  certificate_arn = var.user_pool_domain_certificate_arn
  user_pool_id    = aws_cognito_user_pool.user_pool.id
}


resource "aws_cognito_user_pool" "user_pool" {
  name = var.user_pool_name

  auto_verified_attributes = ["email"]
  deletion_protection = "ACTIVE"

  schema {
    name                     = "email"
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    required                 = true
    string_attribute_constraints {
      min_length = 0
      max_length = 2048
    }
  }

  schema {
    name                     = "accountId"
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      min_length = 44
      max_length = 44
    }
  }

  password_policy {
    minimum_length                   = 8
    require_lowercase                = false
    require_numbers                  = true
    require_symbols                  = false
    require_uppercase                = false
    temporary_password_validity_days = 2
  }

  admin_create_user_config {
    allow_admin_create_user_only = true
  }

  mfa_configuration = "OPTIONAL"
  user_pool_add_ons {
    advanced_security_mode = "ENFORCED"
  }

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }

  sms_authentication_message = <<EOF
Seu codigo ${var.tenant_name}: {####}. Nao compartilhe com ninguem.

#{####}
EOF

  verification_message_template {
    email_subject = "${var.tenant_name} - Recuperação de Senha"
    email_message = "Seu código para recuperação de senha é {####}. Não compartilhe com ninguém"
    sms_message   = "${var.tenant_name} - Seu código para recuperação de senha é {####}. Não compartilhe com ninguém."
  }

  sms_configuration {
    external_id    = "1d858b1a-d793-4ff3-9fd8-889342b08cdf"
    sns_caller_arn = aws_iam_role.user_pool_SMS_role.arn
  }

  email_configuration {
    email_sending_account = "DEVELOPER"
    source_arn            = var.notification_email_sender_arn
  }

  device_configuration {
    device_only_remembered_on_user_prompt = false
    challenge_required_on_new_device      = true
  }

  dynamic "lambda_config" {
    for_each = var.lambda_config_enabled ? [1] : []
    content {
      custom_sms_sender {
        lambda_arn     = module.token_sender.arn
        lambda_version = "V1_0"
      }
      kms_key_id = aws_kms_key.cognito_custom_sender.arn
    }
  }
}

resource "aws_iam_role" "group_role" {
  name = "${var.tenant_name}-user-group-role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Federated": "cognito-identity.amazonaws.com"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringEquals": {
          "cognito-identity.amazonaws.com:aud": "us-east-1:********-dead-beef-cafe-123456790ab",
          "sts:ExternalId": "1d858b1a-d793-4ff3-9fd8-889342b08cdf"
        },
        "ForAnyValue:StringLike": {
          "cognito-identity.amazonaws.com:amr": "authenticated"
        }
      }
    }
  ]
}
EOF
}

resource "aws_iam_role" "user_pool_SMS_role" {
  name               = "${var.tenant_name}_user_pool_SMS_role"
  assume_role_policy = data.aws_iam_policy_document.instance-assume-role-policy.json
}

data "aws_iam_policy_document" "instance-assume-role-policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type = "Service"
      identifiers = ["cognito-idp.amazonaws.com"]
    }
  }
}

resource "aws_iam_policy" "user_pool_sms_policy" {
  name        = "${var.tenant_name}_user_pool_sms_policy"
  description = "A test policy"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "sns:publish"
      ],
      "Resource": [
        "*"
      ]
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "policy_attachment" {
  role       = aws_iam_role.user_pool_SMS_role.name
  policy_arn = aws_iam_policy.user_pool_sms_policy.arn
}

resource "aws_cognito_user_group" "group_admin" {
  name         = "ADMIN"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description  = "Managed by Terraform"
  precedence   = 1
  role_arn     = aws_iam_role.group_role.arn
}

resource "aws_cognito_user_group" "group_owner" {
  name         = "OWNER"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description  = "Managed by Terraform"
  precedence   = 0
  role_arn     = aws_iam_role.group_role.arn
}

resource "aws_cognito_user_group" "group_guest" {
  name         = "GUEST"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description  = "Managed by Terraform"
  precedence   = 2
  role_arn     = aws_iam_role.group_role.arn
}

resource "aws_cognito_user_pool_client" "client" {
  name                                 = "${var.tenant_name}.client"
  user_pool_id                         = aws_cognito_user_pool.user_pool.id
  explicit_auth_flows = [
    "ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH"
  ]
  prevent_user_existence_errors        = "ENABLED"
  access_token_validity                = var.access_token_validity
  id_token_validity                    = var.id_token_validity
  allowed_oauth_scopes = ["email", "openid", "profile"]
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_flows = ["code"]
  callback_urls = ["https://use.via1.app/autenticacao-federado"]
  logout_urls                          = var.logout_urls
  supported_identity_providers = ["COGNITO"]
  token_validity_units {
    access_token  = "minutes"
    id_token      = "minutes"
    refresh_token = "days"
  }

}
