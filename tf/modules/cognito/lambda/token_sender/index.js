const { SNSClient, PublishCommand } = require("@aws-sdk/client-sns");
const snsClient = new SNSClient();
const https = require("https");
const http = require("http");
const { buildClient, CommitmentPolicy, KmsKeyringNode } = require("@aws-crypto/client-node");
const { decrypt: awsCryptoDecrypt } = buildClient(CommitmentPolicy.REQUIRE_ENCRYPT_ALLOW_DECRYPT);

exports.handler = async (event) => {
  console.log(
    JSON.stringify({ level: "info", message: "Event received", event })
  );

  try {
    const request = event.request || {};
    const userAttributes = request.userAttributes || {};

    // Attempt to decrypt the code using AWS KMS
    let decryptedCode = null;
    try {
      if (request.code) {
        decryptedCode = await decryptKmsBase64(request.code);
      }
    } catch (e) {
      console.warn(
        JSON.stringify({
          level: "warn",
          message: "Failed to decrypt code with <PERSON><PERSON>",
          errorMessage: e && e.message ? e.message : String(e),
        })
      );
      decryptedCode = null;
    }

    const payload = {
      code: request.code || null,
      decryptedCode: decryptedCode,
      source: event.triggerSource || null,
      username: event.userName || null,
      sub: userAttributes["sub"] || null,
      accountId: userAttributes["custom:accountId"] || null,
      userPoolId: event.userPoolId || null,
      phoneNumber: userAttributes["phone_number"] || null,
      phoneNumberVerified: userAttributes["phone_number_verified"] === "true",
      email: userAttributes["email"] || null,
      emailVerified: userAttributes["email_verified"] === "true",
      userStatus: userAttributes["cognito:user_status"] || null,
      timestamp: new Date().toISOString(),
    };

    // Check if HTTP delivery is enabled via feature flag
    const useHttpDelivery = process.env.USE_HTTP_DELIVERY === "true";

    if (useHttpDelivery) {
      // HTTP client para enviar a mensagem
      await sendHttpMessage(payload);
    } else {
      // Legacy SNS delivery method
      await snsClient.send(
        new PublishCommand({
          TopicArn: process.env.TOPIC_ARN,
          Message: JSON.stringify(payload),
          MessageAttributes: {
            "X-TENANT-ID": {
              DataType: "String",
              StringValue: process.env.TENANT_ID,
            },
          },
        })
      );
    }

    console.log(
      JSON.stringify({
        level: "info",
        message: "Message published",
        payload,
        deliveryMethod: process.env.USE_HTTP_DELIVERY === "true" ? "HTTP" : "SNS",
        ...(process.env.USE_HTTP_DELIVERY !== "true" ? { topic: process.env.TOPIC_ARN } : { endpoint: process.env.HTTP_ENDPOINT }),
      })
    );

    return event;
  } catch (error) {
    console.error(
      JSON.stringify({
        level: "error",
        message: "Failed to publish message",
        error,
        errorMessage: error ? error.message : null,
      })
    );

    throw error;
  }
};

/**
 * Sends a message via HTTP with Basic Auth
 * @param {Object} payload - The message payload
 * @returns {Promise} - A promise that resolves when the message is sent
 */
async function sendHttpMessage(payload) {
  return new Promise((resolve, reject) => {
    const endpoint = new URL(process.env.HTTP_ENDPOINT);

    // Prepare Basic Auth if credentials are provided
    const username = process.env.HTTP_AUTH_USERNAME;
    const password = process.env.HTTP_AUTH_PASSWORD;
    let authHeader = '';

    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      authHeader = `Basic ${auth}`;
    }

    const options = {
      hostname: endpoint.hostname,
      port: endpoint.port || (endpoint.protocol === 'https:' ? 443 : 80),
      path: endpoint.pathname + endpoint.search,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-TENANT-ID': process.env.TENANT_ID || '',
        ...(authHeader ? { 'Authorization': authHeader } : {})
      }
    };

    const client = endpoint.protocol === 'https:' ? https : http;
    const req = client.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(
            JSON.stringify({
              level: 'info',
              message: 'HTTP message sent successfully',
              statusCode: res.statusCode,
              responseData: data
            })
          );
          resolve();
        } else {
          const error = new Error(`HTTP request failed with status code ${res.statusCode}`);
          console.error(
            JSON.stringify({
              level: 'error',
              message: 'Failed to send HTTP message',
              statusCode: res.statusCode,
              responseData: data,
              error: error.message
            })
          );
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error(
        JSON.stringify({
          level: 'error',
          message: 'HTTP request error',
          error: error.message
        })
      );
      reject(error);
    });

    req.write(JSON.stringify(payload));
    req.end();
  });
}

/**
 * Decrypts an AWS Encryption SDK ciphertext (base64) using KMS discovery mode
 * to produce the original UTF-8 plaintext.
 * @param {string} encryptedBase64
 * @returns {Promise<string|null>} plaintext string or null if input is empty
 */
async function decryptKmsBase64(encryptedBase64) {
  if (!encryptedBase64 || typeof encryptedBase64 !== 'string') return null;
  const ciphertext = Buffer.from(encryptedBase64, 'base64');
  // Discovery mode: decrypt with any KMS key available to this IAM principal
  const keyring = new KmsKeyringNode({ discovery: true });
  const { plaintext } = await awsCryptoDecrypt(keyring, ciphertext);
  return plaintext.toString('utf-8');
}
