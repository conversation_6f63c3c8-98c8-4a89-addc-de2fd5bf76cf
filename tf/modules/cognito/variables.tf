variable "user_pool_domain_name" {

}

variable "user_pool_domain_certificate_arn" {

}

variable "user_pool_name" {

}

variable "notification_email_sender_arn" {
  type = string
}

variable "access_token_validity" {
  type = number
  default = 60
}

variable "id_token_validity" {
  type = number
  default = 60
}

variable "logout_urls" {
  type = list(string)
}

variable "environment" {
  type = string
}

variable "datadog_key_arn"{
  type = string
}

variable "lambda_config_enabled" {
  description = "Habilita ou desabilita o lambda_config no User Pool"
  type        = bool
}

variable "tenant_name" {
  type = string
}