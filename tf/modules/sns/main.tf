resource "aws_sns_sms_preferences" "sms_prefs" {
  delivery_status_iam_role_arn = aws_iam_role.delivery_status_role.arn
  delivery_status_success_sampling_rate = 100
  default_sms_type = "Transactional"
  #monthly_spend_limit = var.monthly_spend_limit
}

resource "aws_iam_role" "delivery_status_role" {
  description        = "Allow AWS to publish SMS delivery status logs"
  name               = "SMSDeliveryStatusRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

data "aws_iam_policy_document" "assume_role" {
  statement {
    sid    = "StatementAssumeRole"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "delivery_status_role_inline_policy" {
  statement {
    sid = "AllowLogs"
    resources = ["*"]
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:PutMetricFilter",
      "logs:PutRetentionPolicy",
    ]
  }
}

resource "aws_iam_role_policy" "delivery_status_role_inline_policy" {
  name   = "${aws_iam_role.delivery_status_role.name}InlinePolicy"
  role   = aws_iam_role.delivery_status_role.id
  policy = data.aws_iam_policy_document.delivery_status_role_inline_policy.json
}