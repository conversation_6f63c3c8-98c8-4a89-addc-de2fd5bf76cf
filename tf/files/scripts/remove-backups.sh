#!/bin/bash

# Nome do Backup Vault (substitua pelo nome do seu vault)
BACKUP_VAULT_NAME="dynamodb-backup-vault"

# Listar todos os Recovery Points do Vault
RECOVERY_POINTS=$(aws backup list-recovery-points-by-backup-vault \
    --backup-vault-name $BACKUP_VAULT_NAME \
    --query 'RecoveryPoints[].RecoveryPointArn' \
    --output text)

# Verificar se há recovery points
if [ -z "$RECOVERY_POINTS" ]; then
  echo "Nenhum recovery point encontrado no vault $BACKUP_VAULT_NAME."
  exit 0
fi

# Loop para excluir cada recovery point
echo "Excluindo recovery points do vault $BACKUP_VAULT_NAME..."

for RECOVERY_POINT_ARN in $RECOVERY_POINTS
do
  echo "Excluindo recovery point: $RECOVERY_POINT_ARN"
  aws backup delete-recovery-point \
      --backup-vault-name $BACKUP_VAULT_NAME \
      --recovery-point-arn $RECOVERY_POINT_ARN
  
  # Verificar se o comando foi bem-sucedido
  if [ $? -eq 0 ]; then
    echo "Recovery point $RECOVERY_POINT_ARN excluído com sucesso."
  else
    echo "Erro ao excluir o recovery point $RECOVERY_POINT_ARN."
  fi
done

echo "Todos os recovery points foram excluídos do vault $BACKUP_VAULT_NAME."
