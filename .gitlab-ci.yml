image:
  name: hashicorp/terraform:light
  entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'

default:
  tags:
    - motorola-stg

stages:
  - build

build:
  stage: build
  script:
    - export TENANT=motorola-staging
    - terraform version
    - rm -rf tf/tenants/envs/.terraform
    - rm -rf tf/tenants/ci/.terraform
    - rm -rf tf/tenants/.terraform
    - cd tf/tenants/envs
    - terraform init -backend=false -var-file=$TENANT/auto.tfvars
    - terraform validate
    - cd ..
    - terraform init -backend=false -var-file=envs/$TENANT/auto.tfvars
    - terraform validate
    - cd ci
    - terraform init -backend=false -var-file=../envs/$TENANT/auto.tfvars
    - terraform validate