
# TL;DR
```bash
# base directory of tenant and Makefile
cd /tf/tenants

export AWS_REGION=us-east-1
export TF_TENANT=motorola-prod

# Terraform backend
make env-init
make env-apply

# TENANT environment
make init
make apply

# CI runners
make ci-init
make ci-secrets
make ci-apply

```
# Objetivo

Este projeto é um trabalho em andamento que visa permitir a criação de novos ambientes Friday de modo mais amigável e automatizado.

Muitos módulos permanecem intactos em relação às versões anteriores e estão disponíveis em /tf/modules.

O que distingue esta nova estratégia é a organização dos tenants, presentes no diretório /tf/tenants.

Existem alguns root modules neste diretório com propósitos específicos:
- **/tf/tenants/envs**: inicia a criação de um novo ambiente Friday e mantém as variáveis de cada ambiente
- **/tf/tenants**: módulo que realmente define a estrutura específica do tenant
- **/tf/tenants/ci**: módulo que cria o ambiente de CI/CD para o tenant

Em `/tf/tenants` existe um `Makefile` que visa simplificar a execução de comandos rotineiros.
Isto porque, ao parametrizar as variáveis de cada tenant e também do Terraform Backend, faz-se necessário invocá-los as referências destes arquivos:

```bash
terraform init -backend-config=envs/$(TENANT)/config.s3.tfbackend -var-file=envs/$(TENANT)/auto.tfvars
```

Por ser pouco prático e repetitivo, o Makefile visa tornar a execução mais amigável.

## Criando a estrutura pela primeira vez

Configure as variáveis de ambiente de acordo com as necessidades de seu ambiente:
```bash
export AWS_REGION=us-east-1
export TF_TENANT=motorola-staging
 
# export TF_TENANT=motorola-prod
# export AWS_PROFILE="motorola"
```

Certifique-se de que todas as variáveis necessárias estão presentes nos arquivos:
- `envs/$(TF_TENANT)/auto.tfvars` 
- `envs/$(TF_TENANT)/config.s3.tfbackend`


## Crie o Terraform Backend:
```
make env-init
make env-plan
make env-apply
```

É esperado que este comando crie o bucket no S3 e o DynamoDB para o lock responsáveis por manter o estado dos demais módulos.

## Crie o ambiente do tenant:
```
make init
make plan
make apply
```

## Crie o ambiente de CI/CD:

- É necessário que exista um Group Gitlab Runner disponível para seu tenant. 
- A partir dele será possível obter o token que os runners utilizarão para se registrar ao subir na AWS.
- Este token deve ser armazenado no AWS Parameter Store. 
- Ele será criado com o valor `CHANGEME` em `/tf/gitlab_runner_group_token.tf` a partir do comando `make ci-secrets`.

1. Inicialize o projeto e crie os tokens
```
make ci-init
make ci-secrets
```
2. Lembre-se de alterar o valor do token manualmente
3. Siga com as instruções finais para concluir
```
make ci-plan
make ci-apply
```